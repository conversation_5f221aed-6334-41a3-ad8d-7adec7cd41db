package com.xc.boot.core.security.service;

import com.mybatisflex.core.query.QueryWrapper;
import com.xc.boot.common.exception.BusinessException;
import com.xc.boot.common.result.ResultCode;
import com.xc.boot.core.security.model.SysUserDetails;
import com.xc.boot.system.mapper.CompanyMapper;
import com.xc.boot.system.model.dto.UserAuthInfo;
import com.xc.boot.system.model.entity.CompanyEntity;
import com.xc.boot.system.service.UserMerchantService;
import com.xc.boot.system.service.UserRoleService;
import com.xc.boot.system.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 系统用户认证 DetailsService
 *
 * <AUTHOR>
 * @since 2021/10/19
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SysUserDetailsService implements UserDetailsService {

    private final UserService userService;
    private final CompanyMapper companyMapper;
    private final UserRoleService userRoleService;
    private final UserMerchantService userMerchantService;

    /**
     * 根据用户名获取用户信息
     *
     * @param username 用户名
     * @return 用户信息
     * @throws UsernameNotFoundException 用户名未找到异常
     */
    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        try {
            UserAuthInfo userAuthInfo = userService.getUserAuthInfo(username);
            if (userAuthInfo == null) {
                throw new BusinessException(ResultCode.USER_NOT_EXIST);
            }
            if (userAuthInfo.getStatus().equals(0)) {
                throw new BusinessException(ResultCode.USER_ACCOUNT_LOCKED);
            }
            SysUserDetails sysUserDetails = new SysUserDetails(userAuthInfo);
            // 封装登录用户详细信息
            // 公司
            CompanyEntity companyEntity = companyMapper.selectOneByQuery(QueryWrapper.create().where(CompanyEntity::getId).eq(userAuthInfo.getCompanyId()));
            if (companyEntity.getStatus().equals(0)) {
                throw new BusinessException(ResultCode.COMPANY_ACCOUNT_LOCKED);
            }
            LocalDateTime expirationDate = companyEntity.getExpirationDate();
            if (Objects.nonNull(expirationDate)) {
                if (expirationDate.isBefore(LocalDateTime.now())) {
                    throw new BusinessException(ResultCode.COMPANY_ACCOUNT_EXPIRED);
                }
            }
            sysUserDetails.setCompany(companyEntity);
            return sysUserDetails;
        } catch (Exception e) {
            // 记录异常日志
            log.error("认证异常:{}", e.getMessage());
            // 抛出异常
            throw e;
        }
    }
}
