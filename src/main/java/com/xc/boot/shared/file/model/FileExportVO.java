package com.xc.boot.shared.file.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 文件导出VO
 */
@Data
@Schema(description = "文件导出VO")
public class FileExportVO {

    @Schema(description = "所属商家")
    private String companyName;

    @Schema(description = "文件名称")
    private String fullName;

    @Schema(description = "文件类型")
    private String extension;

    @Schema(description = "文件大小")
    private String sizeStr;

    @Schema(description = "上传人员")
    private String uploaderName;

    @Schema(description = "上传日期")
    private String createdAtStr;

    @Schema(description = "使用状态")
    private String status;

    @Schema(description = "使用描述")
    private String description;
} 