package com.xc.boot.modules.merchant.service.impl;

import cn.hutool.core.bean.BeanUtil;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.row.Db;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.common.util.CommonUtils;
import com.xc.boot.common.util.OpLogUtils;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.income.mapper.GoodsIncomeMapper;
import com.xc.boot.modules.income.model.entity.GoodsIncomeEntity;
import com.xc.boot.modules.merchant.mapper.GoodsColumnMapper;
import com.xc.boot.modules.merchant.mapper.GoodsIncomeTemplateDetailMapper;
import com.xc.boot.modules.merchant.mapper.GoodsIncomeTemplateMapper;
import com.xc.boot.modules.merchant.model.dto.GoodsIncomeTemplateFormDTO;
import com.xc.boot.modules.merchant.model.entity.GoodsColumnEntity;
import com.xc.boot.modules.merchant.model.entity.GoodsIncomeTemplateDetailEntity;
import com.xc.boot.modules.merchant.model.entity.GoodsIncomeTemplateEntity;
import com.xc.boot.modules.merchant.model.query.GoodsIncomeTemplatePageQuery;
import com.xc.boot.modules.merchant.model.vo.GoodsColumnVO;
import com.xc.boot.modules.merchant.model.vo.GoodsIncomeTemplateDetailVO;
import com.xc.boot.modules.merchant.model.vo.GoodsIncomeTemplateDetailWithColumnVO;
import com.xc.boot.modules.merchant.model.vo.GoodsIncomeTemplatePageVO;
import com.xc.boot.modules.merchant.service.GoodsColumnService;
import com.xc.boot.modules.merchant.service.GoodsIncomeTemplateService;

import com.xc.boot.common.util.listFill.ListFillService;
import com.xc.boot.common.util.listFill.ListFillUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import static com.xc.boot.modules.merchant.model.entity.table.GoodsIncomeTemplateDetailTableDef.GOODS_INCOME_TEMPLATE_DETAIL;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 入库模板服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GoodsIncomeTemplateServiceImpl extends ServiceImpl<GoodsIncomeTemplateMapper, GoodsIncomeTemplateEntity>
        implements GoodsIncomeTemplateService {

    private final ListFillService listFillService;
    private final GoodsColumnMapper goodsColumnMapper;
    private final GoodsIncomeTemplateDetailMapper goodsIncomeTemplateDetailMapper;
    private final GoodsColumnService goodsColumnService;
    private final GoodsIncomeMapper goodsIncomeMapper;

    /**
     * 不可编辑: 是否启用
     * 
     * 货品条码, 货品名称, 所属大类, 所属小类, 成本价, 标签价
     */
    private static final Set<String> NOT_EDITABLE_ENABLED = Set.of("goods_sn", "name", "category_id", "subclass_id",
            "cost_price", "tag_price");

    /**
     * 不可编辑: 是否必填
     * 
     * 货品条码, 货品名称, 所属大类, 所属小类, 成本价, 标签价, 销售方式
     */
    private static final Set<String> NOT_EDITABLE_REQUIRED = Set.of("goods_sn", "name", "category_id",
            "subclass_id",
            "cost_price", "tag_price", "sales_type");

    /**
     * 不可编辑: 默认值
     * 
     * 货品条码, 货品图片
     */
    private static final Set<String> NOT_EDITABLE_DEFAULT_VALUE = Set.of("goods_sn", "image");

    /**
     * 默认必填
     * 
     * 货品条码, 货品名称, 所属大类, 所属小类, 柜台, 数量, 成本价, 标签价, 销售方式, 成色
     */
    private static final Set<String> DEFAULT_REQUIRED = Set.of("goods_sn", "name", "category_id", "subclass_id",
            "counter_id", "stock_num", "cost_price", "tag_price", "sales_type", "quality_id");

    @Override
    public Page<GoodsIncomeTemplatePageVO> getTemplatePage(GoodsIncomeTemplatePageQuery queryParams) {
        // 构建查询条件
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select(
                        QueryMethods.column(GoodsIncomeTemplateEntity::getId),
                        QueryMethods.column(GoodsIncomeTemplateEntity::getName),
                        QueryMethods.column(GoodsIncomeTemplateEntity::getCategoryId),
                        QueryMethods.column(GoodsIncomeTemplateEntity::getDefaultFlag),
                        QueryMethods.column(GoodsIncomeTemplateEntity::getStatus),
                        QueryMethods.column(GoodsIncomeTemplateEntity::getRemark),
                        QueryMethods.column(GoodsIncomeTemplateEntity::getCreatedAt))
                .from(GoodsIncomeTemplateEntity.class)
                // 商户ID条件
                .where(GoodsIncomeTemplateEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                // 模板名称模糊查询
                .and(GoodsIncomeTemplateEntity::getName)
                .like(queryParams.getName(), StringUtils.hasText(queryParams.getName()))
                // 所属大类精确匹配
                .and(GoodsIncomeTemplateEntity::getCategoryId)
                .eq(queryParams.getCategoryId(), queryParams.getCategoryId() != null)
                // 状态精确匹配
                .and(GoodsIncomeTemplateEntity::getStatus)
                .eq(queryParams.getStatus(), queryParams.getStatus() != null)
                // 是否默认精确匹配
                .and(GoodsIncomeTemplateEntity::getDefaultFlag)
                .eq(queryParams.getDefaultFlag(), queryParams.getDefaultFlag() != null)
                // 按ID降序排序
                .orderBy(GoodsIncomeTemplateEntity::getId, false);

        // 执行分页查询
        Page<GoodsIncomeTemplatePageVO> page = mapper.paginateAs(queryParams.getPageNum(),
                queryParams.getPageSize(),
                queryWrapper, GoodsIncomeTemplatePageVO.class);

        // 处理大类文本
        if (page.getRecords() != null && !page.getRecords().isEmpty()) {
            Set<Integer> categoryIds = page.getRecords().stream()
                    .map(GoodsIncomeTemplatePageVO::getCategoryId)
                    .collect(Collectors.toSet());

            ListFillUtil.of(page.getRecords())
                    .build(listFillService::getCategoryNameById, categoryIds, "categoryId",
                            "categoryName")
                    .handle();
        }

        return page;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveTemplate(GoodsIncomeTemplateFormDTO form) {
        // 转换为实体对象
        GoodsIncomeTemplateEntity entity = BeanUtil.copyProperties(form, GoodsIncomeTemplateEntity.class);

        // 设置商户ID
        entity.setCompanyId(SecurityUtils.getCompanyId().intValue());

        // 如果是默认模板，需要将同一大类下的其他模板设置为非默认
        if (entity.getDefaultFlag() == 1) {
            GoodsIncomeTemplateEntity updateEntity = new GoodsIncomeTemplateEntity();
            updateEntity.setDefaultFlag(0);
            this.update(updateEntity, QueryWrapper.create()
                    .where(GoodsIncomeTemplateEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                    .and(GoodsIncomeTemplateEntity::getCategoryId).eq(entity.getCategoryId())
                    .and(GoodsIncomeTemplateEntity::getId).ne(entity.getId()));
        } else {
            // 如果不是默认模板，检查当前大类下是否存在默认模板
            boolean hasDefaultTemplate = this.exists(QueryWrapper.create()
                    .where(GoodsIncomeTemplateEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                    .and(GoodsIncomeTemplateEntity::getCategoryId).eq(entity.getCategoryId())
                    .and(GoodsIncomeTemplateEntity::getDefaultFlag).eq(1));

            CommonUtils.abortIf(!hasDefaultTemplate, "当前大类下不存在默认模板，请先创建默认模板");
        }

        // 检查大类下名称是否重复
        CommonUtils.abortIf(this.exists(QueryWrapper.create()
                .where(GoodsIncomeTemplateEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                .and(GoodsIncomeTemplateEntity::getCategoryId).eq(entity.getCategoryId())
                .and(GoodsIncomeTemplateEntity::getName).eq(entity.getName())
                .and(GoodsIncomeTemplateEntity::getId).ne(entity.getId())), "当前大类下已存在同名模板");

        // 新增时生成默认详情
        Boolean isCreate = entity.getId() == null;

        // 保存模板
        boolean result = this.saveOrUpdate(entity);

        if (isCreate) {
            generateDetails(entity);
        }

        // 记录操作日志
        if (result) {
            OpLogUtils.appendOpLog(
                    "入库模板-" + (entity.getId() == null ? "新增" : "修改"),
                    "模板名称：" + entity.getName(),
                    entity);
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteTemplate(Long id) {
        // 检查是否为默认模板
        GoodsIncomeTemplateEntity template = this.getById(id);
        CommonUtils.abortIf(template == null, "模板不存在");
        CommonUtils.abortIf(template.getDefaultFlag() == 1, "默认模板不允许删除");

        // 检查是否存在关联数据
        Long incomeCount = goodsIncomeMapper.selectCountByQuery(
                QueryWrapper.create()
                        .where(GoodsIncomeEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                        .and(GoodsIncomeEntity::getTemplateId).eq(id));

        CommonUtils.abortIf(incomeCount > 0, "该模板已被入库单使用，不允许删除");

        // 删除模板
        boolean result = this.removeById(id);

        // 记录操作日志
        if (result) {
            OpLogUtils.appendOpLog(
                    "入库模板-删除",
                    "模板ID：" + id,
                    null);
        }

        return result;
    }

    private void generateDetails(GoodsIncomeTemplateEntity entity) {
        Long companyId = SecurityUtils.getCompanyId();
        // 获取商户字段列表
        List<GoodsColumnEntity> columnList = goodsColumnService.getGoodsColumnListByCompanyId(companyId);

        // 生成详情
        List<GoodsIncomeTemplateDetailEntity> detailList = columnList.stream()
                .filter(column -> !column.getSign().equals("supplier_id")
                        && !column.getSign().equals("merchant_id")
                        && !column.getSign().equals("category_id"))
                .map(column -> {
                    GoodsIncomeTemplateDetailEntity detail = new GoodsIncomeTemplateDetailEntity();

                    detail.setCompanyId(companyId.intValue());
                    detail.setTemplateId(entity.getId().intValue());
                    detail.setSign(column.getSign());

                    detail.setEnabled(1);
                    // 设置是否可编辑: 是否启用
                    detail.setEditableEnabled(
                            NOT_EDITABLE_ENABLED.contains(column.getSign()) ? 0 : 1);

                    // 设置是否可编辑: 是否必填
                    detail.setEditableRequired(
                            NOT_EDITABLE_REQUIRED.contains(column.getSign()) ? 0 : 1);

                    // 设置是否可编辑: 默认值
                    detail.setEditableDefaultValue(
                            NOT_EDITABLE_DEFAULT_VALUE.contains(column.getSign()) ? 0 : 1);

                    // 设置是否默认必填
                    detail.setRequiredFlag(DEFAULT_REQUIRED.contains(column.getSign()) ? 1 : 0);

                    // 设置默认值
                    detail.setDefaultValue("");
                    if (column.getSign().equals("num")) {
                        detail.setDefaultValue("1");
                    } 

                    // 设置是否默认图片ID
                    detail.setImageId(0L);

                    return detail;
                }).collect(Collectors.toList());

        // 批量插入
        goodsIncomeTemplateDetailMapper.insertBatch(detailList);
    }

    @Override
    public List<GoodsIncomeTemplateDetailWithColumnVO> getTemplateDetails(Long templateId, String name) {
        // 构建查询条件
        QueryWrapper queryWrapper = QueryWrapper.create()
                .from(GoodsIncomeTemplateDetailEntity.class)
                .select(GOODS_INCOME_TEMPLATE_DETAIL.ALL_COLUMNS)
                // 商户ID条件
                .where(GoodsIncomeTemplateDetailEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                // 模板ID条件
                .and(GoodsIncomeTemplateDetailEntity::getTemplateId).eq(templateId)
                // 按ID升序排序
                .orderBy(GoodsIncomeTemplateDetailEntity::getId, true);

        List<GoodsIncomeTemplateDetailWithColumnVO> details = goodsIncomeTemplateDetailMapper
                .selectListByQueryAs(queryWrapper, GoodsIncomeTemplateDetailWithColumnVO.class);

        // 已有字段标识
        Set<String> signs = details.stream().map(GoodsIncomeTemplateDetailWithColumnVO::getSign)
                .collect(Collectors.toSet());

        // 获取商户字段列表
        List<GoodsColumnEntity> columnList = goodsColumnService
                .getGoodsColumnListByCompanyId(SecurityUtils.getCompanyId());

        // 填充不在模板中的字段
        columnList = columnList.stream().filter(column -> !signs.contains(column.getSign()))
                .filter(column -> !column.getSign().equals("supplier_id")
                        && !column.getSign().equals("merchant_id")
                        && !column.getSign().equals("category_id"))
                .collect(Collectors.toList());
        if (!columnList.isEmpty()) {
            for (GoodsColumnEntity column : columnList) {
                GoodsIncomeTemplateDetailWithColumnVO detail = new GoodsIncomeTemplateDetailWithColumnVO();
                detail.setCompanyId(SecurityUtils.getCompanyId().intValue());
                detail.setTemplateId(templateId.intValue());
                detail.setEnabled(0); // 追加的字段不启用
                detail.setEditableEnabled(1);
                detail.setEditableRequired(1);
                detail.setEditableDefaultValue(1);
                detail.setRequiredFlag(0);
                detail.setDefaultValue("");
                detail.setSign(column.getSign());
                detail.setColumn(BeanUtil.copyProperties(column, GoodsColumnVO.class));
                detail.setImageId(0L);
                details.add(detail);
            }
        }

        // 填充字段信息
        if (!details.isEmpty()) {
            ListFillUtil.of(details)
                    .build(listFillService::getGoodsColumnBySign,
                            details.stream().map(
                                    GoodsIncomeTemplateDetailWithColumnVO::getSign)
                                    .collect(Collectors.toSet()),
                            "sign", "column")
                    .handle();
        }

        if (StringUtils.hasText(name)) {
            details = details.stream().filter(detail -> detail.getColumn() != null
                    && detail.getColumn().getName().contains(name))
                    .collect(Collectors.toList());
        }

        return details;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveTemplateDetails(Long templateId, List<GoodsIncomeTemplateDetailEntity> details) {
        if (details == null || details.isEmpty()) {
            return false;
        }

        // 检查模板是否存在
        boolean exists = this.exists(QueryWrapper.create()
                .where(GoodsIncomeTemplateEntity::getId).eq(templateId)
                .and(GoodsIncomeTemplateEntity::getCompanyId).eq(SecurityUtils.getCompanyId()));
        CommonUtils.abortIf(!exists, "模板不存在");

        // 校验num字段的默认值
        for (GoodsIncomeTemplateDetailEntity detail : details) {
            if ("num".equals(detail.getSign()) && detail.getDefaultValue() != null) {
                try {
                    double defaultValue = Double.parseDouble(detail.getDefaultValue());
                    CommonUtils.abortIf(defaultValue <= 0, "数量的默认值必须大于0");
                } catch (NumberFormatException e) {
                    CommonUtils.abortIf(true, "数量的默认值必须是有效的数字");
                }
            }
        }

        // 获取原有明细
        List<GoodsIncomeTemplateDetailEntity> existingDetails = goodsIncomeTemplateDetailMapper
                .selectListByQuery(
                        QueryWrapper.create()
                                .where(GoodsIncomeTemplateDetailEntity::getTemplateId)
                                .eq(templateId.intValue())
                                .and(GoodsIncomeTemplateDetailEntity::getCompanyId)
                                .eq(SecurityUtils.getCompanyId()));

        // 设置商户ID
        details.forEach(detail -> detail.setCompanyId(SecurityUtils.getCompanyId().intValue()));

        // 分离新增和更新的记录
        List<GoodsIncomeTemplateDetailEntity> toInsert = new ArrayList<>();
        List<GoodsIncomeTemplateDetailEntity> toUpdate = new ArrayList<>();
        Set<Long> existingIds = existingDetails.stream()
                .map(GoodsIncomeTemplateDetailEntity::getId)
                .collect(Collectors.toSet());
        Set<Long> newIds = details.stream()
                .map(GoodsIncomeTemplateDetailEntity::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 收集需要更新的图片ID
        Set<Long> newImageIds = new HashSet<>();
        Set<Long> oldImageIds = new HashSet<>();

        for (GoodsIncomeTemplateDetailEntity detail : details) {
            if (detail.getId() == null) {
                // 新增记录
                detail.setTemplateId(templateId.intValue());
                toInsert.add(detail);
                if (detail.getImageId() != null && detail.getImageId() > 0) {
                    newImageIds.add(detail.getImageId());
                }
            } else {
                // 更新记录
                toUpdate.add(detail);
                if (detail.getImageId() != null && detail.getImageId() > 0) {
                    newImageIds.add(detail.getImageId());
                }
            }
        }

        // 收集原有图片ID
        for (GoodsIncomeTemplateDetailEntity detail : existingDetails) {
            if (detail.getImageId() != null && detail.getImageId() > 0) {
                oldImageIds.add(detail.getImageId());
            }
        }

        // 需要删除的记录
        Set<Long> toDeleteIds = existingIds.stream()
                .filter(id -> !newIds.contains(id))
                .collect(Collectors.toSet());

        // 执行批量操作
        if (!toDeleteIds.isEmpty()) {
            goodsIncomeTemplateDetailMapper.deleteByQuery(
                    QueryWrapper.create()
                            .where(GoodsIncomeTemplateDetailEntity::getId).in(toDeleteIds));
        }

        if (!toInsert.isEmpty()) {
            goodsIncomeTemplateDetailMapper.insertBatchSelective(toInsert);
        }

        if (!toUpdate.isEmpty()) {
            // 构建 UNION ALL 语句
            StringBuilder unionSql = new StringBuilder();
            for (GoodsIncomeTemplateDetailEntity detail : toUpdate) {
                if (unionSql.length() > 0) {
                    unionSql.append(" UNION ALL ");
                }
                unionSql.append("SELECT ")
                        .append(detail.getId()).append(" as id, ")
                        .append(detail.getEnabled()).append(" as enabled, ")
                        .append(detail.getRequiredFlag()).append(" as required_flag, ")
                        .append(detail.getDefaultValue() == null ? "''"
                                : "'" + detail.getDefaultValue() + "'")
                        .append(" as default_value, ")
                        .append(detail.getImageId()).append(" as image_id");
            }

            // 使用 UNION ALL 进行批量更新
            String updateSql = "UPDATE goods_income_template_details as d " +
                    "JOIN (" + unionSql + ") as updates ON d.id = updates.id " +
                    "SET d.enabled = updates.enabled, " +
                    "d.required_flag = updates.required_flag, " +
                    "d.default_value = updates.default_value, " +
                    "d.image_id = updates.image_id " +
                    "WHERE d.id IN ("
                    + toUpdate.stream().map(d -> d.getId().toString())
                            .collect(Collectors.joining(","))
                    + ")";

            Db.updateBySql(updateSql);
        }

        // 更新图片状态
        // 1. 将新增的图片状态设置为已使用
        if (!newImageIds.isEmpty()) {
            CommonUtils.batchUpdateFileStatus(new ArrayList<>(newImageIds), 1);
        }

        // 2. 将不再使用的图片状态设置为未使用
        Set<Long> unusedImageIds = oldImageIds.stream()
                .filter(id -> !newImageIds.contains(id))
                .collect(Collectors.toSet());
        if (!unusedImageIds.isEmpty()) {
            CommonUtils.batchUpdateFileStatus(new ArrayList<>(unusedImageIds), 0);
        }

        return true;
    }

    @Override
    public List<GoodsIncomeTemplateDetailVO> getTemplateDetailsWithColumn(Long id) {
        return getTemplateDetailsWithColumn(id, false);
    }

    @Override
    public List<GoodsIncomeTemplateDetailVO> getTemplateDetailsWithColumn(Long id, Boolean includeDisabled) {
        // 获取基础数据
        List<GoodsIncomeTemplateDetailWithColumnVO> details = getTemplateDetails(id, null);

        // 转换为VO并根据includeDisabled参数过滤记录
        List<GoodsIncomeTemplateDetailVO> voList = details.stream()
                .filter(detail -> includeDisabled || detail.getEnabled() == 1)
                .map(detail -> {
                    GoodsIncomeTemplateDetailVO vo = new GoodsIncomeTemplateDetailVO();
                    vo.setId(detail.getId());
                    vo.setSign(detail.getSign());
                    vo.setEnabled(detail.getEnabled());
                    vo.setRequiredFlag(detail.getRequiredFlag());
                    vo.setDefaultValue(detail.getDefaultValue());
                    vo.setImageId(detail.getImageId());
                    return vo;
                })
                .collect(Collectors.toList());

        // 使用ListFillUtil填充column信息
        if (!voList.isEmpty()) {
            ListFillUtil.of(voList)
                    .build(listFillService::getGoodsColumnBySign,
                            voList.stream().map(GoodsIncomeTemplateDetailVO::getSign)
                                    .collect(Collectors.toSet()),
                            "sign", "column")
                    .handle();
        }

        return voList;
    }

    @Override
    public GoodsIncomeTemplateEntity getTemplateById(Long id) {
        return this.getById(id);
    }
}