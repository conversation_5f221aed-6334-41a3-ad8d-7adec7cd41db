package com.xc.boot.modules.income.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import jakarta.servlet.http.HttpServletResponse;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.mybatisflex.core.query.QueryWrapper;
import com.xc.boot.common.base.IBaseEnum;
import com.xc.boot.common.base.CustomColumnItemDTO;
import com.xc.boot.common.enums.CategoryEnum;
import com.xc.boot.common.util.CommonUtils;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.income.model.dto.GoodsIncomeTemplateExportDTO;
import com.xc.boot.modules.income.model.vo.GoodsIncomeDetailVO;
import com.xc.boot.modules.income.service.GoodsIncomeParseTemplateService;
import com.xc.boot.modules.merchant.mapper.*;
import com.xc.boot.modules.merchant.model.entity.*;
import com.xc.boot.modules.merchant.model.entity.GoodsColumnEntity;
import com.xc.boot.modules.merchant.model.entity.GoodsIncomeTemplateDetailEntity;
import com.xc.boot.modules.merchant.model.enums.GoodsColumnCategoryEnum;
import com.xc.boot.common.util.PriceUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 入库单模板服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GoodsIncomeParseTemplateServiceImpl implements GoodsIncomeParseTemplateService {

    /**
     * 入库单模板详情Mapper
     */
    private final GoodsIncomeTemplateDetailMapper goodsIncomeTemplateDetailMapper;

    /**
     * 商品字段Mapper
     */
    private final GoodsColumnMapper goodsColumnMapper;

    /**
     * 柜台Mapper
     */
    private final CounterMapper counterMapper;

    /**
     * 子类Mapper
     */
    private final SubclassMapper subclassMapper;

    /**
     * 品牌Mapper
     */
    private final BrandMapper brandMapper;

    /**
     * 款式Mapper
     */
    private final StyleMapper styleMapper;

    /**
     * 成色Mapper
     */
    private final QualityMapper qualityMapper;

    /**
     * 工艺Mapper
     */
    private final TechnologyMapper technologyMapper;

    /**
     * 珠石Mapper
     */
    private final JewelryMapper jewelryMapper;

    /**
     * 关联数据映射
     * 用于存储各种关联数据的名称到ID的映射关系
     */
    private static class AssociationMapping {
        /**
         * 柜台名称到ID的映射
         */
        private final Map<String, Long> counterMap = new HashMap<>();

        /**
         * 子类名称到ID的映射
         */
        private final Map<String, Long> subclassMap = new HashMap<>();

        /**
         * 品牌名称到ID的映射
         */
        private final Map<String, Long> brandMap = new HashMap<>();

        /**
         * 款式名称到ID的映射
         */
        private final Map<String, Long> styleMap = new HashMap<>();

        /**
         * 成色名称到ID的映射
         */
        private final Map<String, Long> qualityMap = new HashMap<>();

        /**
         * 工艺名称到ID的映射
         */
        private final Map<String, Long> technologyMap = new HashMap<>();

        /**
         * 珠石名称到ID的映射
         */
        private final Map<String, Long> jewelryMap = new HashMap<>();

        /**
         * 自定义字段映射
         * key: 字段标识
         * value: 字段信息
         */
        private final Map<String, GoodsColumnEntity> customColumnMap = new HashMap<>();

        /**
         * 小类与大类的关联映射
         * key: 小类ID
         * value: 大类ID
         */
        private final Map<Long, Integer> subclassCategoryMap = new HashMap<>();
    }

    /**
     * 关联数据映射实例
     */
    private AssociationMapping associationMapping;

    /**
     * 初始化关联数据映射
     */
    private void initAssociationMappings() {
        associationMapping = new AssociationMapping();
        
        // 获取所有关联数据
        List<CounterEntity> counters = counterMapper.selectListByQuery(
            QueryWrapper.create()
                .where(CounterEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
        );
        List<SubclassEntity> subclasses = subclassMapper.selectListByQuery(
            QueryWrapper.create()
                .where(SubclassEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
        );
        List<BrandEntity> brands = brandMapper.selectListByQuery(
            QueryWrapper.create()
                .where(BrandEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
        );
        List<StyleEntity> styles = styleMapper.selectListByQuery(
            QueryWrapper.create()
                .where(StyleEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
        );
        List<QualityEntity> qualities = qualityMapper.selectListByQuery(
            QueryWrapper.create()
                .where(QualityEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
        );
        List<TechnologyEntity> technologies = technologyMapper.selectListByQuery(
            QueryWrapper.create()
                .where(TechnologyEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
        );
        List<JewelryEntity> jewelries = jewelryMapper.selectListByQuery(
            QueryWrapper.create()
                .where(JewelryEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
        );

        // 构建映射
        counters.forEach(counter -> associationMapping.counterMap.put(counter.getName(), counter.getId()));
        subclasses.forEach(subclass -> {
            associationMapping.subclassMap.put(subclass.getName(), subclass.getId());
            // 构建小类与大类的关联映射
            associationMapping.subclassCategoryMap.put(subclass.getId(), subclass.getCategoryId());
        });
        brands.forEach(brand -> associationMapping.brandMap.put(brand.getName(), brand.getId()));
        styles.forEach(style -> associationMapping.styleMap.put(style.getName(), style.getId()));
        qualities.forEach(quality -> associationMapping.qualityMap.put(quality.getName(), quality.getId()));
        technologies.forEach(technology -> associationMapping.technologyMap.put(technology.getName(), technology.getId()));
        jewelries.forEach(jewelry -> associationMapping.jewelryMap.put(jewelry.getName(), jewelry.getId()));

        // 获取自定义字段
        List<GoodsColumnEntity> customColumns = goodsColumnMapper.selectListByQuery(
            QueryWrapper.create()
                .where(GoodsColumnEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                .where(GoodsColumnEntity::getCategory).eq(GoodsColumnCategoryEnum.CUSTOM.getValue())
        );

        // 构建自定义字段映射
        customColumns.forEach(column -> associationMapping.customColumnMap.put(column.getSign(), column));
    }

    @Override
    public void exportTemplate(GoodsIncomeTemplateExportDTO dto) {
        // 查询模板详情
        List<GoodsIncomeTemplateDetailEntity> details = goodsIncomeTemplateDetailMapper.selectListByQuery(
            QueryWrapper.create()
                .where(GoodsIncomeTemplateDetailEntity::getTemplateId).eq(dto.getTemplateId())
                .and(GoodsIncomeTemplateDetailEntity::getSign).ne("image")
                .and(GoodsIncomeTemplateDetailEntity::getEnabled).eq(1)
                .orderBy(GoodsIncomeTemplateDetailEntity::getId).asc()
        );

        // 过滤掉图片类型的自定义字段
        details = details.stream()
            .filter(detail -> {
                // 通过字段的sign获取字段信息
                GoodsColumnEntity column = CommonUtils.getGoodsColumnsBySign(detail.getSign());
                // 如果是图片类型(type=6)，则过滤掉
                return column == null || !Integer.valueOf(6).equals(column.getType());
            })
            .collect(Collectors.toList());

        // 查询字段列表
        List<GoodsColumnEntity> columns = goodsColumnMapper.selectListByQuery(
            QueryWrapper.create()
                .where(GoodsColumnEntity::getCompanyId).eq(SecurityUtils.getCompanyId().intValue())
        );
        // 字段 map <sign, name>
        Map<String, String> columnMap = columns.stream()
            .collect(Collectors.toMap(GoodsColumnEntity::getSign, GoodsColumnEntity::getName));

        // 获取表头
        List<String> headers = details.stream()
            .map(detail -> columnMap.get(detail.getSign()))
            .collect(Collectors.toList());

        // 获取响应对象
        HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
        try {
            // 设置响应头
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("货品入库模板", StandardCharsets.UTF_8).replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=" + fileName + ".xlsx");

            // 创建空数据列表
            List<Map<String, String>> dataList = new ArrayList<>();
            
            // 使用EasyExcel导出
            EasyExcel.write(response.getOutputStream())
                .head(headers.stream().map(header -> List.of(header)).collect(Collectors.toList()))
                .sheet("货品入库模板")
                .registerWriteHandler(new SimpleColumnWidthStyleStrategy(15)) // 设置固定列宽为15个字符
                .doWrite(dataList);
        } catch (IOException e) {
            throw new RuntimeException("导出模板失败", e);
        }
    }

    /**
     * 获取模板字段映射
     *
     * @param templateId 模板ID
     * @return 包含字段名称到字段标识的映射和字段标识到是否必填的映射
     */
    private Map<String, Object> getTemplateColumnMap(Long templateId) {
        // 查询模板字段
        List<GoodsIncomeTemplateDetailEntity> templateDetails = goodsIncomeTemplateDetailMapper.selectListByQuery(
            QueryWrapper.create()
                .where(GoodsIncomeTemplateDetailEntity::getTemplateId).eq(templateId)
                .and(GoodsIncomeTemplateDetailEntity::getEnabled).eq(1)
        );

        if (CollUtil.isEmpty(templateDetails)) {
            CommonUtils.abort("模板未配置任何字段");
        }

        // 获取所有字段标识
        Set<String> signs = templateDetails.stream()
            .map(GoodsIncomeTemplateDetailEntity::getSign)
            .collect(Collectors.toSet());

        // 查询字段信息
        List<GoodsColumnEntity> columns = goodsColumnMapper.selectListByQuery(
            QueryWrapper.create()
                .where(GoodsColumnEntity::getSign).in(signs)
        );

        // 构建字段名称到字段标识的映射
        Map<String, String> columnMap = columns.stream()
            .collect(Collectors.toMap(
                GoodsColumnEntity::getName,
                GoodsColumnEntity::getSign,
                (v1, v2) -> v1
            ));

        // 构建字段标识到是否必填的映射
        Map<String, Integer> requiredMap = templateDetails.stream()
            .collect(Collectors.toMap(
                GoodsIncomeTemplateDetailEntity::getSign,
                GoodsIncomeTemplateDetailEntity::getRequiredFlag,
                (v1, v2) -> v1
            ));

        // 构建字段标识到字段名称的映射（用于错误消息）
        Map<String, String> signToNameMap = columns.stream()
            .collect(Collectors.toMap(
                GoodsColumnEntity::getSign,
                GoodsColumnEntity::getName,
                (v1, v2) -> v1
            ));

        Map<String, Object> result = new HashMap<>();
        result.put("columnMap", columnMap);
        result.put("requiredMap", requiredMap);
        result.put("signToNameMap", signToNameMap);
        return result;
    }

    /**
     * 获取Excel表头
     *
     * @param reader Excel读取器
     * @return 表头列表
     */
    private List<String> getExcelHeaders(ExcelReader reader) {
        // 获取第一行作为表头
        List<List<Object>> rows = reader.read(0);
        if (CollUtil.isEmpty(rows)) {
            CommonUtils.abort("Excel文件内容为空");
        }

        // 获取表头
        List<Object> headerRow = rows.get(0);
        return headerRow.stream()
            .map(Object::toString)
            .collect(Collectors.toList());
    }

    /**
     * 验证表头
     *
     * @param headers 表头列表
     * @param templateColumnMap 模板字段映射
     */
    private void validateHeaders(List<String> headers, Map<String, String> templateColumnMap) {
        // 检查是否存在模板中未定义的字段
        List<String> invalidHeaders = headers.stream()
            .filter(header -> !templateColumnMap.containsKey(header))
            .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(invalidHeaders)) {
            CommonUtils.abort("模板中存在未定义的字段：" + String.join("、", invalidHeaders));
        }

        // 检查是否缺少模板中定义的必填字段
        List<String> missingHeaders = templateColumnMap.keySet().stream()
            .filter(header -> !headers.contains(header))
            .filter(header -> !header.equals("货品图片"))
            .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(missingHeaders)) {
            CommonUtils.abort("模板中缺少字段：" + String.join("、", missingHeaders));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<GoodsIncomeDetailVO> parseTemplate(MultipartFile file, Long templateId) {
        // 1. 验证文件
        if (file == null || file.isEmpty()) {
            CommonUtils.abort("文件不能为空");
        }

        // 2. 验证模板ID
        if (templateId == null) {
            CommonUtils.abort("模板ID不能为空");
        }

        // 3. 初始化关联数据映射
        initAssociationMappings();

        // 4. 读取Excel文件
        ExcelReader reader;
        try {
            reader = ExcelUtil.getReader(file.getInputStream());
        } catch (IOException e) {
            CommonUtils.abort("读取文件失败：" + e.getMessage());
            return null;
        }

        // 5. 获取模板字段映射
        Map<String, Object> templateMaps = getTemplateColumnMap(templateId);
        @SuppressWarnings("unchecked")
        Map<String, String> columnMap = (Map<String, String>) templateMaps.get("columnMap");
        @SuppressWarnings("unchecked")
        Map<String, Integer> requiredMap = (Map<String, Integer>) templateMaps.get("requiredMap");
        @SuppressWarnings("unchecked")
        Map<String, String> signToNameMap = (Map<String, String>) templateMaps.get("signToNameMap");

        // 6. 获取并验证表头
        List<String> headers = getExcelHeaders(reader);
        validateHeaders(headers, columnMap);

        // 7. 读取所有数据
        List<Map<String, Object>> rows = reader.readAll();

        // 8. 验证数据
        if (CollUtil.isEmpty(rows)) {
            CommonUtils.abort("Excel文件内容为空");
        }

        // 9. 转换数据
        List<GoodsIncomeDetailVO> result = new ArrayList<>();
        for (int i = 0; i < rows.size(); i++) {
            Map<String, Object> row = rows.get(i);
            GoodsIncomeDetailVO vo = new GoodsIncomeDetailVO();

            try {
                // 遍历模板字段映射
                for (Map.Entry<String, String> entry : columnMap.entrySet()) {
                    String fieldName = entry.getKey();  // 字段名称
                    String sign = entry.getValue();     // 字段标识

                    // 根据sign获取值并设置到VO对象
                    setFieldValue(vo, sign, row.get(fieldName), i + 2, requiredMap, signToNameMap); // i + 2 是因为Excel从1开始，且第1行是表头
                }

                // 校验关联关系
                validateAssociations(vo, i + 2);

                result.add(vo);
            } catch (Exception e) {
                CommonUtils.abort("第" + (i + 2) + "行数据解析失败：" + e.getMessage());
            }
        }

        return result;
    }

    /**
     * 设置字段值
     *
     * @param vo 目标对象
     * @param sign 字段标识
     * @param value 字段值
     * @param rowNum 行号
     * @param requiredMap 字段标识到是否必填的映射
     * @param signToNameMap 字段标识到字段名称的映射
     */
    private void setFieldValue(GoodsIncomeDetailVO vo, String sign, Object value, int rowNum,
            Map<String, Integer> requiredMap, Map<String, String> signToNameMap) {
        String strValue = value == null ? "" : value.toString().trim();
        String rowPrefix = "第" + rowNum + "行：";

        // 必填字段校验
        if (StrUtil.isBlank(strValue) && Integer.valueOf(1).equals(requiredMap.get(sign))) {
            String fieldName = signToNameMap.get(sign);
            CommonUtils.abort(rowPrefix + fieldName + "不能为空");
            return;
        }

        // 如果没有值，对于字符串字段返回空字符串而不是null
        if (StrUtil.isBlank(strValue)) {
            strValue = "";
        }

        // 处理自定义字段
        GoodsColumnEntity customColumn = associationMapping.customColumnMap.get(sign);
        if (customColumn != null) {
            // 如果是下拉选择类型(type=5)，需要校验选项
            if (Integer.valueOf(5).equals(customColumn.getType()) && StrUtil.isNotBlank(strValue)) {
                validateSelectOptions(customColumn, strValue, rowPrefix, signToNameMap.get(sign));
            }

            CustomColumnItemDTO columnItem = new CustomColumnItemDTO();
            columnItem.setColumnId(customColumn.getId().intValue());
            columnItem.setColumnSign(customColumn.getSign());
            columnItem.setType(customColumn.getType());
            columnItem.setSecretLevel(customColumn.getSecretLevel());
            columnItem.setNumberPrecision(customColumn.getNumberPrecision());
            columnItem.setValue(StrUtil.isBlank(strValue) ? "" : strValue);

            // 添加到自定义列列表
            if (vo.getCustomColumn() == null) {
                vo.setCustomColumn(new ArrayList<>());
            }
            vo.getCustomColumn().add(columnItem);
            return;
        }

        switch (sign) {
            case "goods_id":
                // 商品ID，默认为0
                vo.setGoodsId(0);
                break;
            case "category_id":
                // 商品类别ID，通过中文名称获取对应的枚举值
                Object categoryValue = IBaseEnum.getValueByLabel(strValue, CategoryEnum.class);
                CommonUtils.abortIf(categoryValue == null, rowPrefix + "无法识别的大类: " + strValue);
                vo.setCategoryId(((Long) categoryValue).intValue());
                break;
            case "counter_id":
                // 柜台ID，通过柜台名称获取对应的ID
                Long counterId = associationMapping.counterMap.get(strValue);
                CommonUtils.abortIf(counterId == null, rowPrefix + "无法识别的柜台: " + strValue);
                vo.setCounterId(counterId.intValue());
                break;
            case "subclass_id":
                // 子类ID，通过子类名称获取对应的ID
                Long subclassId = associationMapping.subclassMap.get(strValue);
                CommonUtils.abortIf(subclassId == null, rowPrefix + "无法识别的小类: " + strValue);
                vo.setSubclassId(subclassId.intValue());
                break;
            case "brand_id":
                // 品牌ID，通过品牌名称获取对应的ID
                Long brandId = associationMapping.brandMap.get(strValue);
                CommonUtils.abortIf(brandId == null, rowPrefix + "无法识别的品牌: " + strValue);
                vo.setBrandId(brandId.intValue());
                break;
            case "style_id":
                // 款式ID，通过款式名称获取对应的ID
                Long styleId = associationMapping.styleMap.get(strValue);
                CommonUtils.abortIf(styleId == null, rowPrefix + "无法识别的款式: " + strValue);
                vo.setStyleId(styleId.intValue());
                break;
            case "quality_id":
                // 成色ID，通过成色名称获取对应的ID
                Long qualityId = associationMapping.qualityMap.get(strValue);
                CommonUtils.abortIf(qualityId == null, rowPrefix + "无法识别的成色: " + strValue);
                vo.setQualityId(qualityId.intValue());
                break;
            case "technology_id":
                // 工艺ID，通过工艺名称获取对应的ID
                Long technologyId = associationMapping.technologyMap.get(strValue);
                CommonUtils.abortIf(technologyId == null, rowPrefix + "无法识别的工艺: " + strValue);
                vo.setTechnologyId(technologyId.intValue());
                break;
            case "main_stone_id":
                // 主石ID，通过珠石名称获取对应的ID
                Long mainStoneId = associationMapping.jewelryMap.get(strValue);
                CommonUtils.abortIf(mainStoneId == null, rowPrefix + "无法识别的主石: " + strValue);
                vo.setMainStoneId(mainStoneId.intValue());
                break;
            case "sub_stone_id":
                // 辅石ID，通过珠石名称获取对应的ID
                Long subStoneId = associationMapping.jewelryMap.get(strValue);
                CommonUtils.abortIf(subStoneId == null, rowPrefix + "无法识别的辅石: " + strValue);
                vo.setSubStoneId(subStoneId.intValue());
                break;
            case "goods_sn":
                // 商品编号
                vo.setGoodsSn(strValue);
                break;
            case "name":
                // 商品名称
                vo.setName(strValue);
                break;
            case "sales_type":
                // 销售类型
                vo.setSalesType(strValue);
                break;
            case "batch_no":
                // 批次号
                vo.setBatchNo(strValue);
                break;
            case "cert_no":
                // 证书号
                vo.setCertNo(strValue);
                break;
            case "remark":
                // 备注
                vo.setRemark(strValue);
                break;
            case "weight":
                // 重量
                vo.setWeight(getBigDecimalValue(value));
                break;
            case "net_gold_weight":
                // 净金重
                vo.setNetGoldWeight(getBigDecimalValue(value));
                break;
            case "net_silver_weight":
                // 净银重
                vo.setNetSilverWeight(getBigDecimalValue(value));
                break;
            case "main_stone_count":
                // 主石数量
                vo.setMainStoneCount(getIntegerValue(value));
                break;
            case "main_stone_weight":
                // 主石重量
                vo.setMainStoneWeight(getBigDecimalValue(value));
                break;
            case "sub_stone_count":
                // 辅石数量
                vo.setSubStoneCount(getIntegerValue(value));
                break;
            case "sub_stone_weight":
                // 辅石重量
                vo.setSubStoneWeight(getBigDecimalValue(value));
                break;
            case "circle_size":
                // 圈口
                vo.setCircleSize(strValue);
                break;
            case "cost_price":
                // 成本价 - 输入为元
                vo.setCostPrice(getBigDecimalValue(value));
                break;
            case "gold_price":
                // 金价 - 输入为元
                vo.setGoldPrice(getBigDecimalValue(value));
                break;
            case "silver_price":
                // 银价 - 输入为元
                vo.setSilverPrice(getBigDecimalValue(value));
                break;
            case "work_price":
                // 工费 - 输入为元
                vo.setWorkPrice(getBigDecimalValue(value));
                break;
            case "cert_price":
                // 证书费 - 输入为元
                vo.setCertPrice(getBigDecimalValue(value));
                break;
            case "sale_work_price":
                // 销售工费 - 输入为元
                vo.setSaleWorkPrice(getBigDecimalValue(value));
                break;
            case "tag_price":
                // 标签价 - 输入为元
                vo.setTagPrice(getBigDecimalValue(value));
                break;
            case "num":
                // 数量
                vo.setNum(getIntegerValue(value));
                break;
        }
    }

    /**
     * 校验关联关系
     *
     * @param vo 入库单明细VO
     * @param rowNum 行号
     */
    private void validateAssociations(GoodsIncomeDetailVO vo, int rowNum) {
        String rowPrefix = "第" + rowNum + "行：";

        // 校验小类与大类的关联关系
        if (vo.getSubclassId() != null && vo.getCategoryId() != null) {
            Integer expectedCategoryId = associationMapping.subclassCategoryMap.get(vo.getSubclassId().longValue());
            if (expectedCategoryId != null && !expectedCategoryId.equals(vo.getCategoryId())) {
                CommonUtils.abort(rowPrefix + "小类与大类不匹配，该小类不属于所选择的大类");
            }
        }
    }

    /**
     * 校验下拉选择字段的选项
     *
     * @param customColumn 自定义字段信息
     * @param value 填写的值
     * @param rowPrefix 行前缀
     * @param fieldName 字段名称
     */
    private void validateSelectOptions(GoodsColumnEntity customColumn, String value, String rowPrefix, String fieldName) {
        String options = customColumn.getOptions();
        if (StrUtil.isBlank(options)) {
            return; // 如果没有配置选项，则不校验
        }

        try {
            JSONArray optionArray = JSONUtil.parseArray(options);
            boolean found = false;

            for (Object option : optionArray) {
                if (option instanceof JSONObject) {
                    JSONObject optionObj = (JSONObject) option;
                    String label = optionObj.getStr("label");
                    if (value.equals(label)) {
                        found = true;
                        break;
                    }
                }
            }

            if (!found) {
                // 获取所有可选项用于错误提示
                List<String> validOptions = new ArrayList<>();
                for (Object option : optionArray) {
                    if (option instanceof JSONObject) {
                        JSONObject optionObj = (JSONObject) option;
                        String label = optionObj.getStr("label");
                        if (StrUtil.isNotBlank(label)) {
                            validOptions.add(label);
                        }
                    }
                }
                CommonUtils.abort(rowPrefix + fieldName + "的值[" + value + "]不在可选项中，可选项为：" + String.join("、", validOptions));
            }
        } catch (Exception e) {
            log.warn("解析自定义字段选项失败：{}", e.getMessage());
        }
    }

    /**
     * 获取整数值
     */
    private Integer getIntegerValue(Object value) {
        if (value == null) {
            return null;
        }
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        String strValue = value.toString();
        return StrUtil.isBlank(strValue) ? null : Integer.parseInt(strValue);
    }

    /**
     * 获取长整数值
     */
    private Long getLongValue(Object value) {
        if (value == null) {
            return null;
        }
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        String strValue = value.toString();
        return StrUtil.isBlank(strValue) ? null : Long.parseLong(strValue);
    }

    /**
     * 获取BigDecimal值
     */
    private BigDecimal getBigDecimalValue(Object value) {
        if (value == null) {
            return null;
        }
        if (value instanceof Number) {
            return new BigDecimal(value.toString());
        }
        String strValue = value.toString();
        return StrUtil.isBlank(strValue) ? null : new BigDecimal(strValue);
    }
} 