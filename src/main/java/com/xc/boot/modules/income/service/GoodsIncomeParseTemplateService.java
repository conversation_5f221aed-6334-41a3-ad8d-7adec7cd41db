package com.xc.boot.modules.income.service;

import com.xc.boot.modules.income.model.dto.GoodsIncomeTemplateExportDTO;
import com.xc.boot.modules.income.model.vo.GoodsIncomeDetailVO;

import java.util.List;

import org.springframework.web.multipart.MultipartFile;

/**
 * 入库单模板服务接口
 */
public interface GoodsIncomeParseTemplateService {
    
    /**
     * 导出模板
     *
     * @param dto 导出DTO
     */
    void exportTemplate(GoodsIncomeTemplateExportDTO dto);
    
    /**
     * 解析模板
     *
     * @param file 文件
     * @param templateId 模板ID
     * @return 解析结果列表
     */
    List<GoodsIncomeDetailVO> parseTemplate(MultipartFile file, Long templateId);
} 