package com.xc.boot.modules.income.controller;

import com.mybatisflex.core.paginate.Page;
import com.xc.boot.common.base.DeleteRequest;
import com.xc.boot.common.result.PageResult;
import com.xc.boot.common.result.Result;
import com.xc.boot.common.util.ColumnEncryptUtil;
import com.xc.boot.modules.income.model.dto.GoodsIncomeAuditDTO;
import com.xc.boot.modules.income.model.dto.GoodsIncomeCreateDTO;
import com.xc.boot.modules.income.model.dto.GoodsIncomeDetailUpdateDTO;
import com.xc.boot.modules.income.model.dto.GoodsIncomeTemplateExportDTO;
import com.xc.boot.modules.income.model.dto.GoodsSnCheckDTO;
import com.xc.boot.modules.income.model.query.GoodsIncomeDetailPageQuery;
import com.xc.boot.modules.income.model.query.GoodsIncomePageQuery;
import com.xc.boot.modules.income.model.vo.GoodsIncomeDetailPageVO;
import com.xc.boot.modules.income.model.vo.GoodsIncomeDetailVO;
import com.xc.boot.modules.income.model.vo.GoodsIncomePageVO;
import com.xc.boot.modules.income.model.vo.GoodsIncomeInfoVO;
import com.xc.boot.modules.income.model.vo.GoodsSnCheckVO;
import com.xc.boot.modules.income.service.GoodsIncomeAuditService;
import com.xc.boot.modules.income.service.GoodsIncomeService;
import com.xc.boot.modules.merchant.model.enums.GoodsColumnTypeEnum;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

import com.xc.boot.modules.income.service.GoodsIncomeParseTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

/**
 * 货品入库控制层
 */
@Tag(name = "货品管理-采购入库单")
@RestController
@RequestMapping("/api/income")
@RequiredArgsConstructor
public class GoodsIncomeController {
    
    private final GoodsIncomeService goodsIncomeService;
    private final GoodsIncomeParseTemplateService templateService;
    private final GoodsIncomeAuditService auditService;
    
    @Operation(summary = "创建入库单")
    @PostMapping("/create")
    public Result<Integer> create(@RequestBody @Valid GoodsIncomeCreateDTO dto) {
        Integer id = goodsIncomeService.create(dto);
        return Result.success(id);
    }

    @Operation(summary = "获取导入模板")
    @PostMapping("/template")
    public void exportTemplate(@RequestBody @Valid GoodsIncomeTemplateExportDTO dto) {
        templateService.exportTemplate(dto);
    }

    @Operation(summary = "解析模板")
    @PostMapping(value = "/template/parse", consumes = "multipart/form-data")
    public Result<List<GoodsIncomeDetailVO>> parseTemplate(
        @RequestPart("file") MultipartFile file,
        @RequestParam("templateId") Long templateId
    ) {
        List<GoodsIncomeDetailVO> list = templateService.parseTemplate(file, templateId);
        return Result.success(list);
    }

    @Operation(summary = "入库单分页列表")
    @PostMapping("/page")
    public PageResult<?> getIncomePage(@RequestBody GoodsIncomePageQuery queryParams) {
        Page<GoodsIncomePageVO> page = goodsIncomeService.getIncomePage(queryParams);
        return PageResult.success((Page<?>)ColumnEncryptUtil.process(page));
    }

    @Operation(summary = "删除入库单")
    @PostMapping("/delete")
    public Result<Boolean> deleteIncome(@RequestBody @Valid DeleteRequest request) {
        boolean result = goodsIncomeService.deleteIncome(request.getId());
        return Result.success(result);
    }

    @Operation(summary = "审核入库单")
    @PostMapping("/audit")
    public Result<Boolean> auditIncome(@RequestBody @Valid GoodsIncomeAuditDTO dto) {
        boolean result = auditService.audit(dto.getIds());
        return Result.success(result);
    }

    @Operation(summary = "获取入库单详情")
    @PostMapping("/info")
    public Result<?> getIncomeInfo(@RequestBody @Valid DeleteRequest request) {
        GoodsIncomeInfoVO info = goodsIncomeService.getIncomeInfo(request.getId());
        List<JSONObject> list = ColumnEncryptUtil.encrypt(List.of(info), GoodsIncomeInfoVO.class, null);
        return Result.success(list.get(0));
    }

    @Operation(summary = "入库单明细分页列表")
    @PostMapping("/detail/page")
    public PageResult<?> getIncomeDetailPage(@RequestBody @Valid GoodsIncomeDetailPageQuery queryParams) {
        Page<GoodsIncomeDetailPageVO> page = goodsIncomeService.getIncomeDetailPage(queryParams);
        Page<?> result = (Page<?>) ColumnEncryptUtil.process(page);
        return PageResult.success(result);
    }

    @Operation(summary = "删除入库单明细")
    @PostMapping("/detail/delete")
    public Result<Boolean> deleteIncomeDetail(@RequestBody @Valid DeleteRequest request) {
        boolean result = goodsIncomeService.deleteIncomeDetail(request.getId());
        return Result.success(result);
    }

    @Operation(summary = "检查商品条码")
    @PostMapping("/goods/check")
    public Result<?> checkGoodsSn(@RequestBody @Valid GoodsSnCheckDTO dto) {
        GoodsSnCheckVO result = goodsIncomeService.checkGoodsSn(dto);
        Object obj = ColumnEncryptUtil.process(result);

        // JSONObject
        JSONObject json = JSONUtil.parseObj(obj);
        Object newColumns = json.get("newColumns");
        if(newColumns instanceof List){
            @SuppressWarnings("unchecked")
            List<JSONObject> list = (List<JSONObject>) newColumns;
            list.forEach(i -> {
                // 图片 json -> 数组
                if(i.getInt("imageId") != 0){
                    i.set("value", JSONUtil.parseArray(i.get("value")));
                }

                // 处理图片空数组
                if(i.getInt("type") == GoodsColumnTypeEnum.IMAGE.getValue() && i.get("value") instanceof String){
                    if(StrUtil.isNotBlank(i.getStr("value"))){
                        i.set("value", JSONUtil.parseArray(i.getStr("value")));
                    }
                }

                // 处理多选的 value
                if(i.getInt("isMultiple") == 1 && i.getInt("type") == GoodsColumnTypeEnum.SELECT.getValue() && i.get("value") instanceof String){
                    if(StrUtil.isNotBlank(i.getStr("value"))){
                        i.set("value", JSONUtil.parseArray(i.getStr("value")));
                    }else{
                        i.set("value", new ArrayList<>());
                    }
                }
            });
        }

        return Result.success(json);
    }

    @Operation(summary = "更新入库单明细")
    @PostMapping("/detail/update")
    public Result<Boolean> updateIncomeDetail(@RequestBody @Valid GoodsIncomeDetailUpdateDTO dto) {
        boolean result = goodsIncomeService.updateIncomeDetail(dto.getId(), dto);
        return Result.success(result);
    }
} 