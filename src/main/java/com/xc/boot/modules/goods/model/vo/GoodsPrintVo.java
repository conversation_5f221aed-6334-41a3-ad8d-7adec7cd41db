package com.xc.boot.modules.goods.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xc.boot.common.annotation.GoodsColumn;
import com.xc.boot.common.base.CustomColumnItemDTO;
import com.xc.boot.common.util.PriceUtil;
import com.xc.boot.modules.goods.model.entity.GoodsHasImagesEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@Schema(description = "货品打印数据列表VO")
public class GoodsPrintVo {
    @Schema(description = "id")
    private Long id;

    @Schema(description = "货品条码")
    @GoodsColumn(value = "goods_sn", signAddList = false)
    private String goodsSn;

    @Schema(description = "货品名称")
    @GoodsColumn(value = "name", signAddList = false)
    private String name;

    @Schema(description = "所属大类")
    @GoodsColumn(value = "category_id", signAddList = false)
    private String category;

    @Schema(description = "所属小类")
    @GoodsColumn(value = "subclass_id", signAddList = false)
    private String subclass;

    @Schema(description = "成色")
    @GoodsColumn(value = "quality_id", signAddList = false)
    private String quality;

    @Schema(description = "款式")
    @GoodsColumn(value = "style_id", signAddList = false)
    private String style;

    @Schema(description = "品牌")
    @GoodsColumn(value = "brand_id", signAddList = false)
    private String brand;

    @Schema(description = "圈口")
    @GoodsColumn(value = "circle_size", signAddList = false)
    private String circleSize;

    @Schema(description = "工艺")
    @GoodsColumn(value = "technology_id", signAddList = false)
    private String technology;

    @Schema(description = "主石")
    @GoodsColumn(value = "main_stone_id", signAddList = false)
    private String mainStone;

    @Schema(description = "主石数")
    @GoodsColumn(value = "main_stone_count", signAddList = false)
    private String mainStoneCount;

    @Schema(description = "主石重(ct)")
    @GoodsColumn(value = "main_stone_weight", signAddList = false)
    private BigDecimal mainStoneWeight;

    @Schema(description = "辅石")
    @GoodsColumn(value = "sub_stone_id", signAddList = false)
    private String subStone;

    @Schema(description = "辅石数")
    @GoodsColumn(value = "sub_stone_count", signAddList = false)
    private String subStoneCount;

    @Schema(description = "辅石重(ct)")
    @GoodsColumn(value = "sub_stone_weight", signAddList = false)
    private BigDecimal subStoneWeight;

    @Schema(description = "批次号")
    @GoodsColumn(value = "batch_no", signAddList = false)
    private String batchNo;

    @Schema(description = "重量(g)")
    @GoodsColumn(value = "weight", signAddList = false)
    private BigDecimal weight;

    @Schema(description = "净金重(g)")
    @GoodsColumn(value = "net_gold_weight", signAddList = false)
    private BigDecimal netGoldWeight;

    @Schema(description = "净银重(g)")
    @GoodsColumn(value = "net_silver_weight", signAddList = false)
    private BigDecimal netSilverWeight;

    @Schema(description = "柜台")
    @GoodsColumn(value = "counter_id", signAddList = false)
    private String counter;

    @Schema(description = "成本单价")
    @GoodsColumn(value = "cost_price", signAddList = false)
    private String costPrice;

    @Schema(description = "成本单价(去零)")
    @GoodsColumn(value = "cost_price", signAddList = false)
    private String costPriceClear;

    @Schema(description = "成本单价(¥)")
    @GoodsColumn(value = "cost_price", signAddList = false)
    private String costPricePreUnit;

    @Schema(description = "成本单价(¥)(去零)")
    @GoodsColumn(value = "cost_price", signAddList = false)
    private String costPricePreUnitClear;

    @Schema(description = "成本单价(元)")
    @GoodsColumn(value = "cost_price", signAddList = false)
    private String costPriceLastUnit;

    @Schema(description = "成本单价(元)(去零)")
    @GoodsColumn(value = "cost_price", signAddList = false)
    private String costPriceLastUnitClear;

    @Schema(description = "工费单价")
    @GoodsColumn(value = "work_price", signAddList = false)
    private String workPrice;

    @Schema(description = "工费单价(去零)")
    @GoodsColumn(value = "work_price", signAddList = false)
    private String workPriceClear;

    @Schema(description = "工费单价(¥)")
    @GoodsColumn(value = "work_price", signAddList = false)
    private String workPricePreUnit;

    @Schema(description = "工费单价(¥)(去零)")
    @GoodsColumn(value = "work_price", signAddList = false)
    private String workPricePreUnitClear;

    @Schema(description = "工费单价(元)")
    @GoodsColumn(value = "work_price", signAddList = false)
    private String workPriceLastUnit;

    @Schema(description = "工费单价(元)(去零)")
    @GoodsColumn(value = "work_price", signAddList = false)
    private String workPriceLastUnitClear;

    @Schema(description = "标签单价")
    @GoodsColumn(value = "tag_price", signAddList = false)
    private String tagPrice;

    @Schema(description = "标签单价(去零)")
    @GoodsColumn(value = "tag_price", signAddList = false)
    private String tagPriceClear;

    @Schema(description = "标签单价(¥)")
    @GoodsColumn(value = "tag_price", signAddList = false)
    private String tagPricePreUnit;

    @Schema(description = "标签单价(¥)(去零)")
    @GoodsColumn(value = "tag_price", signAddList = false)
    private String tagPricePreUnitClear;

    @Schema(description = "标签单价(元)")
    @GoodsColumn(value = "tag_price", signAddList = false)
    private String tagPriceLastUnit;

    @Schema(description = "标签单价(元)(去零)")
    @GoodsColumn(value = "tag_price", signAddList = false)
    private String tagPriceLastUnitClear;

    @Schema(description = "证书费")
    @GoodsColumn(value = "cert_price", signAddList = false)
    private String certPrice;

    @Schema(description = "证书费(去零)")
    @GoodsColumn(value = "cert_price", signAddList = false)
    private String certPriceClear;

    @Schema(description = "证书费(¥)")
    @GoodsColumn(value = "cert_price", signAddList = false)
    private String certPricePreUnit;

    @Schema(description = "证书费(¥)(去零)")
    @GoodsColumn(value = "cert_price", signAddList = false)
    private String certPricePreUnitClear;

    @Schema(description = "证书费(元)")
    @GoodsColumn(value = "cert_price", signAddList = false)
    private String certPriceLastUnit;

    @Schema(description = "证书费(元)(去零)")
    @GoodsColumn(value = "cert_price", signAddList = false)
    private String certPriceLastUnitClear;

    @JsonIgnore
    @Schema(description = "所属柜台ID")
    private Long counterId;

    @JsonIgnore
    @Schema(description = "所属大类ID")
    private Long categoryId;

    @JsonIgnore
    @Schema(description = "所属小类ID")
    private Long subclassId;

    @JsonIgnore
    @Schema(description = "品牌ID")
    private Long brandId;

    @JsonIgnore
    @Schema(description = "款式ID")
    private Long styleId;

    @JsonIgnore
    @Schema(description = "成色ID")
    private Long qualityId;

    @JsonIgnore
    @Schema(description = "工艺ID")
    private Long technologyId;

    @JsonIgnore
    @Schema(description = "主石ID")
    private Long mainStoneId;

    @JsonIgnore
    @Schema(description = "辅石ID")
    private Long subStoneId;

    @JsonIgnore
    @Schema(description = "自定义字段")
    private List<CustomColumnItemDTO> customerColumns = new ArrayList<>();


    public BigDecimal getWeight() {
        return PriceUtil.formatThreeDecimal(weight);
    }

    public BigDecimal getNetGoldWeight() {
        return PriceUtil.formatThreeDecimal(netGoldWeight);
    }

    public BigDecimal getNetSilverWeight() {
        return PriceUtil.formatThreeDecimal(netSilverWeight);
    }

    public BigDecimal getMainStoneWeight() {
        return PriceUtil.formatThreeDecimal(mainStoneWeight);
    }
    
    public BigDecimal getSubStoneWeight() {
        return PriceUtil.formatThreeDecimal(subStoneWeight);
    }
    
    public String getCostPrice() {
        return PriceUtil.formatTwoDecimal(costPrice).toPlainString();
    }

    public String getWorkPrice() {
        return PriceUtil.formatTwoDecimal(workPrice).toPlainString();
    }

    public String getCertPrice() {
        return PriceUtil.formatTwoDecimal(certPrice).toPlainString();
    }

    public String getTagPrice() {
        return PriceUtil.formatTwoDecimal(tagPrice).toPlainString();
    }
}
