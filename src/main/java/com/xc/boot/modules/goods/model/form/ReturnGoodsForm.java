package com.xc.boot.modules.goods.model.form;

import com.xc.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @ClassName ReturnGoodsForm
 * @Date: 2025/6/14 16:01
 * @Description: 描述
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "退货货品查询form")
public class ReturnGoodsForm extends BasePageQuery {
    @Schema(description = "关键字")
    @NotBlank(message = "关键字不能为空")
    private String keyword;

    @Schema(description = "查询方式(1:条码|2:名称|3:编号)")
    @NotNull(message = "查询方式不能为空")
    private Integer type;

    @Schema(description = "门店id")
    @NotNull(message = "门店id不能为空")
    private Integer merchantId;

    @Schema(description = "供应商id")
    @NotNull(message = "供应商id不能为空")
    private Integer supplierId;
}
