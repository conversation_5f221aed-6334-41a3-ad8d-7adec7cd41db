package com.xc.boot.modules.goods.model.query;

import com.xc.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "调拨单分页查询对象")
public class GoodsTransferPageQuery extends BasePageQuery {
    @Schema(description = "调拨单号")
    private String transferSn;
    @Schema(description = "调拨类型")
    private Integer type;
    @Schema(description = "调出门店ID(多个用逗号分隔)")
    private String merchantOutcomeIds;
    @Schema(description = "调入门店ID(多个用逗号分隔)")
    private String merchantIncomeIds;
    @Schema(description = "状态")
    private Integer status;
    @Schema(description = "创建人ID(多个用逗号分隔)")
    private String createdByIds;
    @Schema(description = "审核人ID(多个用逗号分隔)")
    private String auditByIds;
    @Schema(description = "收货人ID(多个用逗号分隔)")
    private String receiptByIds;
    @Schema(description = "创建时间范围")
    private List<String> createdAtRange;
    @Schema(description = "审核时间范围")
    private List<String> auditAtRange;
    @Schema(description = "收货时间范围")
    private List<String> receiptAtRange;
} 