package com.xc.boot.modules.gift.service.impl;

import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.modules.gift.model.entity.GiftEntity;
import com.xc.boot.modules.gift.mapper.GiftMapper;
import com.xc.boot.modules.gift.service.GiftService;
import com.xc.boot.modules.gift.model.query.GiftPageQuery;
import com.xc.boot.modules.gift.model.vo.GiftPageVO;
import com.xc.boot.modules.gift.model.dto.GiftUpdateDTO;
import com.xc.boot.modules.gift.model.entity.GiftHasImagesEntity;
import com.xc.boot.modules.gift.mapper.GiftHasImagesMapper;
import com.xc.boot.common.util.PriceUtil;
import com.xc.boot.common.util.CommonUtils;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.common.util.listFill.ListFillUtil;
import com.xc.boot.common.util.listFill.ListFillService;
import com.xc.boot.common.base.FileItemDTO;
import com.xc.boot.common.util.OpLogUtils;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.query.QueryMethods;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.xc.boot.common.util.QueryUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 赠品服务实现类
 */
@Service
@RequiredArgsConstructor
public class GiftServiceImpl extends ServiceImpl<GiftMapper, GiftEntity> implements GiftService {
    private final GiftHasImagesMapper giftHasImagesMapper;
    private final ListFillService listFillService;

    @Override
    public Page<GiftPageVO> getGiftPage(GiftPageQuery queryParams) {
        // 构建查询条件
        QueryWrapper query = QueryWrapper.create()
            .select(
                QueryMethods.column(GiftEntity::getId),
                QueryMethods.column(GiftEntity::getMerchantId),
                QueryMethods.column(GiftEntity::getSupplierId),
                QueryMethods.column(GiftEntity::getGiftSn),
                QueryMethods.column(GiftEntity::getName),
                QueryMethods.column(GiftEntity::getNum),
                QueryMethods.column(GiftEntity::getStockNum),
                QueryMethods.column(GiftEntity::getSoldNum),
                QueryMethods.column(GiftEntity::getTransferNum),
                QueryMethods.column(GiftEntity::getFrozenNum),
                QueryMethods.column(GiftEntity::getWeight),
                QueryMethods.column(GiftEntity::getCostPrice),
                QueryMethods.column(GiftEntity::getTagPrice)
            )
            // 公司隔离
            .where(GiftEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
            // 门店权限限制
            .and(GiftEntity::getMerchantId).in(SecurityUtils.getMerchantIds());

        // 赠品编号模糊查询
        if (StrUtil.isNotBlank(queryParams.getGiftSn())) {
            query.and(GiftEntity::getGiftSn).like(queryParams.getGiftSn());
        }

        // 赠品名称模糊查询
        if (StrUtil.isNotBlank(queryParams.getName())) {
            query.and(GiftEntity::getName).like(queryParams.getName());
        }

        // 所属门店多选查询
        if (StrUtil.isNotBlank(queryParams.getMerchantIds())) {
            List<Integer> merchantIds = QueryUtils.parseIntegerIds(queryParams.getMerchantIds());
            if (!merchantIds.isEmpty()) {
                query.and(GiftEntity::getMerchantId).in(merchantIds);
            }
        }

        // 供应商多选查询
        if (StrUtil.isNotBlank(queryParams.getSupplierIds())) {
            List<Integer> supplierIds = QueryUtils.parseIntegerIds(queryParams.getSupplierIds());
            if (!supplierIds.isEmpty()) {
                query.and(GiftEntity::getSupplierId).in(supplierIds);
            }
        }

        // 成本单价范围查询
        if (queryParams.getCostPriceStart() != null) {
            query.and(GiftEntity::getCostPrice).ge(PriceUtil.yuan2fen(queryParams.getCostPriceStart()).intValue());
        }
        if (queryParams.getCostPriceEnd() != null) {
            query.and(GiftEntity::getCostPrice).le(PriceUtil.yuan2fen(queryParams.getCostPriceEnd()).intValue());
        }

        // 标签单价范围查询
        if (queryParams.getTagPriceStart() != null) {
            query.and(GiftEntity::getTagPrice).ge(PriceUtil.yuan2fen(queryParams.getTagPriceStart()).intValue());
        }
        if (queryParams.getTagPriceEnd() != null) {
            query.and(GiftEntity::getTagPrice).le(PriceUtil.yuan2fen(queryParams.getTagPriceEnd()).intValue());
        }

        // 默认按ID倒序
        query.orderBy(GiftEntity::getId, false);

        // 执行分页查询
        Page<GiftPageVO> page = this.mapper.paginateAs(
            queryParams.getPageNum(),
            queryParams.getPageSize(),
            query,
            GiftPageVO.class
        );

        // 填充关联数据
        if (page.getRecords() != null && !page.getRecords().isEmpty()) {
            fillGiftPageVOs(page.getRecords());
        }

        return page;
    }

    /**
     * 填充赠品列表关联数据
     */
    private void fillGiftPageVOs(List<GiftPageVO> records) {
        if (records == null || records.isEmpty()) {
            return;
        }

        Set<Long> giftIds = new HashSet<>();
        Set<Integer> merchantIds = new HashSet<>();
        Set<Integer> supplierIds = new HashSet<>();

        for (GiftPageVO vo : records) {
            if (vo.getId() != null) {
                giftIds.add(vo.getId());
            }
            if (vo.getMerchantId() != null) {
                merchantIds.add(vo.getMerchantId());
            }
            if (vo.getSupplierId() != null) {
                supplierIds.add(vo.getSupplierId());
            }
        }

        // 查询图片数据
        Map<Long, List<FileItemDTO>> imageMap = new HashMap<>();
        if (!giftIds.isEmpty()) {
            List<GiftHasImagesEntity> images = giftHasImagesMapper.selectListByQuery(
                QueryWrapper.create()
                    .where(GiftHasImagesEntity::getGiftId).in(giftIds)
                    .and(GiftHasImagesEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
                    .orderBy(GiftHasImagesEntity::getSort, true)
            );

            // 按赠品ID分组图片
            for (GiftHasImagesEntity image : images) {
                Long giftId = image.getGiftId().longValue();
                FileItemDTO fileItem = new FileItemDTO();
                fileItem.setId(image.getImageId().longValue());
                fileItem.setUrl(image.getUrl());
                imageMap.computeIfAbsent(giftId, k -> new ArrayList<>()).add(fileItem);
            }
        }

        // 填充门店和供应商名称
        ListFillUtil.of(records)
            .build(listFillService::getMerchantNameById, merchantIds, "merchantId", "merchantName")
            .build(listFillService::getSupplierNameById, supplierIds, "supplierId", "supplierName")
            .handle();

        // 填充数据
        for (GiftPageVO vo : records) {
            // 价格字段分转元
            if (vo.getCostPrice() != null) {
                vo.setCostPrice(PriceUtil.fen2yuan(vo.getCostPrice()));
            }
            if (vo.getTagPrice() != null) {
                vo.setTagPrice(PriceUtil.fen2yuan(vo.getTagPrice()));
            }

            // 计算总价
            if (vo.getNum() != null) {
                if (vo.getCostPrice() != null) {
                    vo.setTotalCostPrice(vo.getCostPrice().multiply(new BigDecimal(vo.getNum())));
                }
                if (vo.getTagPrice() != null) {
                    vo.setTotalTagPrice(vo.getTagPrice().multiply(new BigDecimal(vo.getNum())));
                }
            }

            // 填充图片
            vo.setImages(imageMap.get(vo.getId()));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateGift(GiftUpdateDTO dto) {
        // 1. 获取赠品信息
        GiftEntity gift = this.getById(dto.getId());
        CommonUtils.abortIf(gift == null, "赠品不存在");
        CommonUtils.abortIf(!gift.getCompanyId().equals(SecurityUtils.getCompanyId().intValue()), "无权操作其他商户的数据");
        // 门店权限检查
        CommonUtils.abortIf(!SecurityUtils.getMerchantIds().contains(gift.getMerchantId()), "无权操作其他门店的数据");

        // 2. 更新基本信息
        boolean hasUpdate = false;
        if (StrUtil.isNotBlank(dto.getName())) {
            gift.setName(dto.getName());
            hasUpdate = true;
        }
        if (dto.getWeight() != null) {
            gift.setWeight(dto.getWeight());
            hasUpdate = true;
        }
        if (dto.getCostPrice() != null) {
            gift.setCostPrice(PriceUtil.yuan2fen(dto.getCostPrice()).intValue());
            hasUpdate = true;
        }
        if (dto.getTagPrice() != null) {
            gift.setTagPrice(PriceUtil.yuan2fen(dto.getTagPrice()).intValue());
            hasUpdate = true;
        }

        // 3. 更新基本信息
        if (hasUpdate) {
            boolean success = this.updateById(gift);
            CommonUtils.abortIf(!success, "更新赠品基本信息失败");
        }

        // 4. 处理图片
        if (dto.getImages() != null) {
            // 获取原有图片
            List<GiftHasImagesEntity> oldImages = giftHasImagesMapper.selectListByQuery(
                QueryWrapper.create()
                    .where(GiftHasImagesEntity::getGiftId).eq(gift.getId())
                    .and(GiftHasImagesEntity::getCompanyId).eq(gift.getCompanyId())
            );

            // 收集原有图片ID和新图片ID
            Set<Long> oldImageIds = oldImages.stream()
                .map(img -> img.getImageId().longValue())
                .collect(Collectors.toSet());
            Set<Long> newImageIds = dto.getImages().stream()
                .map(FileItemDTO::getId)
                .collect(Collectors.toSet());

            // 删除原有图片关联
            if (!oldImages.isEmpty()) {
                giftHasImagesMapper.deleteByQuery(
                    QueryWrapper.create()
                        .where(GiftHasImagesEntity::getGiftId).eq(gift.getId())
                        .and(GiftHasImagesEntity::getCompanyId).eq(gift.getCompanyId())
                );
            }

            // 添加新的图片关联
            if (!dto.getImages().isEmpty()) {
                List<GiftHasImagesEntity> images = new ArrayList<>();
                for (int i = 0; i < dto.getImages().size(); i++) {
                    FileItemDTO image = dto.getImages().get(i);
                    GiftHasImagesEntity imageEntity = new GiftHasImagesEntity();
                    imageEntity.setCompanyId(gift.getCompanyId());
                    imageEntity.setGiftId(gift.getId().intValue());
                    imageEntity.setImageId(image.getId().intValue());
                    imageEntity.setUrl(image.getUrl());
                    imageEntity.setSort(i + 1);
                    images.add(imageEntity);
                }
                giftHasImagesMapper.insertBatch(images);
            }

            // 5. 更新图片状态
            // 将不再使用的图片状态设为未使用
            List<Long> unusedImageIds = oldImageIds.stream()
                .filter(imageId -> !newImageIds.contains(imageId))
                .collect(Collectors.toList());
            if (!unusedImageIds.isEmpty()) {
                CommonUtils.batchUpdateFileStatus(unusedImageIds, 0);
            }
            // 将新使用的图片状态设为已使用
            List<Long> newImageIdsList = new ArrayList<>(newImageIds);
            if (!newImageIdsList.isEmpty()) {
                CommonUtils.batchUpdateFileStatus(newImageIdsList, 1);
            }
        }

        // 6. 记录操作日志
        OpLogUtils.appendOpLog("编辑赠品", "编辑赠品: " + gift.getGiftSn() + ", 名称: " + gift.getName(), dto);

        return true;
    }
} 