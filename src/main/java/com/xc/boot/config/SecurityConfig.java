package com.xc.boot.config;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.hutool.captcha.generator.CodeGenerator;
import com.mybatisflex.core.update.UpdateChain;
import com.xc.boot.common.enums.RequestMethodEnum;
import com.xc.boot.common.util.AnonymousUtils;
import com.xc.boot.config.property.SecurityProperties;
import com.xc.boot.core.filter.RateLimiterFilter;
import com.xc.boot.core.security.exception.MyAccessDeniedHandler;
import com.xc.boot.core.security.exception.MyAuthenticationEntryPoint;
import com.xc.boot.core.security.extension.MockAuthenticationProvider;
import com.xc.boot.core.security.extension.SmsAuthenticationProvider;
import com.xc.boot.core.security.extension.WechatAuthenticationProvider;
import com.xc.boot.core.security.filter.CaptchaValidationFilter;
import com.xc.boot.core.security.filter.JwtValidationFilter;
import com.xc.boot.core.filter.OpLogFilter;
import com.xc.boot.core.security.filter.PermissionFilter;
import com.xc.boot.core.security.service.SysUserDetailsService;
import com.xc.boot.shared.auth.service.impl.RedisTokenService;
import com.xc.boot.system.mapper.CompanyMapper;
import com.xc.boot.system.model.entity.SysUserEntity;
import com.xc.boot.system.service.ConfigService;
import com.xc.boot.system.service.UserMerchantService;
import com.xc.boot.system.service.UserRoleService;
import com.xc.boot.system.service.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.ProviderManager;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.annotation.web.configurers.HeadersConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

import java.util.Map;
import java.util.Set;

/**
 * Spring Security 安全配置
 *
 * <AUTHOR>
 * @since 2023/2/17
 */
@Configuration
@EnableWebSecurity
@EnableMethodSecurity
@RequiredArgsConstructor
public class SecurityConfig {

    private final RedisTemplate<String, Object> redisTemplate;
    private final PasswordEncoder passwordEncoder;

    private final RedisTokenService redisTokenService;
    private final WxMaService wxMaService;
    private final UserService userService;
    private final SysUserDetailsService userDetailsService;

    private final CodeGenerator codeGenerator;
    private final SecurityProperties securityProperties;
    private final ConfigService configService;

    private final MyAuthenticationEntryPoint authenticationEntryPoint; // 项目内安全类
    private final MyAccessDeniedHandler accessDeniedHandler;
    private final CompanyMapper companyMapper;
    private final UserRoleService userRoleService;
    private final UserMerchantService userMerchantService;
    private final ApplicationContext applicationContext;

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        // 获取所有匿名访问路径
        Map<String, Set<String>> anonymousUrls = AnonymousUtils.getAllAnonymousUrls(applicationContext);

        http

                .authorizeHttpRequests(requestMatcherRegistry ->
                        requestMatcherRegistry
                                .requestMatchers(securityProperties.getIgnoreUrls().toArray(new String[0])).permitAll()
                                // GET
                                .requestMatchers(HttpMethod.GET, anonymousUrls.get(RequestMethodEnum.GET.getType()).toArray(new String[0])).permitAll()
                                // POST
                                .requestMatchers(HttpMethod.POST, anonymousUrls.get(RequestMethodEnum.POST.getType()).toArray(new String[0])).permitAll()
                                // PUT
                                .requestMatchers(HttpMethod.PUT, anonymousUrls.get(RequestMethodEnum.PUT.getType()).toArray(new String[0])).permitAll()
                                // PATCH
                                .requestMatchers(HttpMethod.PATCH, anonymousUrls.get(RequestMethodEnum.PATCH.getType()).toArray(new String[0])).permitAll()
                                // DELETE
                                .requestMatchers(HttpMethod.DELETE, anonymousUrls.get(RequestMethodEnum.DELETE.getType()).toArray(new String[0])).permitAll()
                                // 所有类型的接口都放行
                                .requestMatchers(anonymousUrls.get(RequestMethodEnum.ALL.getType()).toArray(new String[0])).permitAll()
                                .anyRequest().authenticated()
                )
                .exceptionHandling(httpSecurityExceptionHandlingConfigurer ->
                        httpSecurityExceptionHandlingConfigurer
                                .authenticationEntryPoint(authenticationEntryPoint)
                                .accessDeniedHandler(accessDeniedHandler)
                )
                .sessionManagement(configurer -> configurer.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
                .csrf(AbstractHttpConfigurer::disable)
                .headers(headers -> headers.frameOptions(HeadersConfigurer.FrameOptionsConfig::disable))
                // 限流过滤器
                .addFilterBefore(new RateLimiterFilter(redisTemplate, configService), UsernamePasswordAuthenticationFilter.class)
                // JWT 校验过滤器
                .addFilterBefore(new JwtValidationFilter(redisTokenService), UsernamePasswordAuthenticationFilter.class)
                // 权限校验过滤器
                .addFilterBefore(new PermissionFilter(), UsernamePasswordAuthenticationFilter.class)
                // 操作日志过滤器
                .addFilterBefore(new OpLogFilter(), UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }

    /**
     * 默认密码认证的 Provider
     */
    @Bean
    public DaoAuthenticationProvider daoAuthenticationProvider() {
        DaoAuthenticationProvider daoAuthenticationProvider = new DaoAuthenticationProvider();
        daoAuthenticationProvider.setPasswordEncoder(passwordEncoder);
        daoAuthenticationProvider.setUserDetailsService(userDetailsService);
        daoAuthenticationProvider.setHideUserNotFoundExceptions(false);
        return daoAuthenticationProvider;
    }

    /**
     * 微信认证 Provider
     */
    @Bean
    public WechatAuthenticationProvider weChatAuthenticationProvider() {
        return new WechatAuthenticationProvider(userService, wxMaService);
    }

    /**
     * sms 认证 Provider
     * @return
     */
    @Bean
    public SmsAuthenticationProvider smsAuthenticationProvider() {
        return new SmsAuthenticationProvider(userService, companyMapper);
    }

    /**
     * 模拟认证 Provider
     */
    @Bean
    public MockAuthenticationProvider mockAuthenticationProvider() {
        return new MockAuthenticationProvider();
    }

    /**
     * 手动注入 AuthenticationManager，支持多种认证方式
     * - DaoAuthenticationProvider：用户名密码认证
     * - WeChatAuthenticationProvider：微信认证
     * - SmsAuthenticationProvider：短信认证
     * - MockAuthenticationProvider：模拟认证
     */
    @Bean
    public AuthenticationManager authenticationManager() {
        return new ProviderManager(daoAuthenticationProvider(), weChatAuthenticationProvider(), smsAuthenticationProvider(), mockAuthenticationProvider());
    }
}
