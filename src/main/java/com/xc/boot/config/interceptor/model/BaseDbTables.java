package com.xc.boot.config.interceptor.model;

import java.util.List;
import java.util.Map;
import java.util.Set;

import com.xc.boot.modules.merchant.model.enums.GoodsColumnSecretLevelEnum;
import com.xc.boot.modules.merchant.model.enums.GoodsColumnTypeEnum;

/**
 * <AUTHOR>
 * @ClassName BaseDbTables
 * @Date: 2025/5/29 16:37
 * @Description: 基础库表名配置
 */
public class BaseDbTables {
    /**
     * 基础库表名
     */
    public static final Set<String> BASE_TABLES = Set.of(
            "company",
            "company_settings",
            "file",
            "sys_config",
            "sys_dept",
            "sys_dict",
            "sys_dict_data",
            "sys_log",
            "sys_menu",
            "merchant",
            "sys_notice",
            "sys_role",
            "sys_role_menu",
            "sys_user",
            "sys_user_notice",
            "sys_user_role",
            "sys_user_merchant",
            "sys_permission",
            "heading_signs",
            "heading_user_cache",
            "sys_role_permission",
            "print_tags",
            "sys_export",
            "login_logs",
            "pda_package"
    // ...
    );

    /**
     * 默认货品字段
     */
    public static final List<Map<String, Object>> BASE_GOODS_COLUMNS = List.of(
            Map.of(
                    "name", "所属门店",
                    "sign", "merchant_id",
                    "secret_level", GoodsColumnSecretLevelEnum.NORMAL.getValue().toString(),
                    "type", GoodsColumnTypeEnum.SELECT.getValue().toString(),
                    "number_precision", 0),
            Map.of(
                    "name", "柜台",
                    "sign", "counter_id",
                    "secret_level", GoodsColumnSecretLevelEnum.NORMAL.getValue().toString(),
                    "type", GoodsColumnTypeEnum.SELECT.getValue().toString(),
                    "number_precision", 0),
            Map.of(
                    "name", "供应商",
                    "sign", "supplier_id",
                    "secret_level", GoodsColumnSecretLevelEnum.NORMAL.getValue().toString(),
                    "type", GoodsColumnTypeEnum.SELECT.getValue().toString(),
                    "number_precision", 0),
            Map.of(
                    "name", "货品条码",
                    "sign", "goods_sn",
                    "secret_level", GoodsColumnSecretLevelEnum.NORMAL.getValue().toString(),
                    "type", GoodsColumnTypeEnum.TEXT.getValue().toString(),
                    "number_precision", 0),
            Map.of(
                    "name", "货品图片",
                    "sign", "image",
                    "secret_level", GoodsColumnSecretLevelEnum.NORMAL.getValue().toString(),
                    "type", GoodsColumnTypeEnum.IMAGE.getValue().toString(),
                    "number_precision", 0),
            Map.of(
                    "name", "货品名称",
                    "sign", "name",
                    "secret_level", GoodsColumnSecretLevelEnum.NORMAL.getValue().toString(),
                    "type", GoodsColumnTypeEnum.TEXT.getValue().toString(),
                    "number_precision", 0),
            Map.of(
                    "name", "数量",
                    "sign", "num",
                    "secret_level", GoodsColumnSecretLevelEnum.NORMAL.getValue().toString(),
                    "type", GoodsColumnTypeEnum.NUMBER.getValue().toString(),
                    "number_precision", 0),
            Map.of(
                    "name", "重量",
                    "sign", "weight",
                    "secret_level", GoodsColumnSecretLevelEnum.NORMAL.getValue().toString(),
                    "type", GoodsColumnTypeEnum.NUMBER.getValue().toString(),
                    "number_precision", 3),
            Map.of(
                    "name", "净金重",
                    "sign", "net_gold_weight",
                    "secret_level", GoodsColumnSecretLevelEnum.NORMAL.getValue().toString(),
                    "type", GoodsColumnTypeEnum.NUMBER.getValue().toString(),
                    "number_precision", 3),
            Map.of(
                    "name", "净银重",
                    "sign", "net_silver_weight",
                    "secret_level", GoodsColumnSecretLevelEnum.NORMAL.getValue().toString(),
                    "type", GoodsColumnTypeEnum.NUMBER.getValue().toString(),
                    "number_precision", 3),
            Map.of(
                    "name", "成本单价",
                    "sign", "cost_price",
                    "secret_level", GoodsColumnSecretLevelEnum.SECRET.getValue().toString(),
                    "type", GoodsColumnTypeEnum.NUMBER.getValue().toString(),
                    "number_precision", 2),
            Map.of(
                    "name", "金进单价",
                    "sign", "gold_price",
                    "secret_level", GoodsColumnSecretLevelEnum.SECRET.getValue().toString(),
                    "type", GoodsColumnTypeEnum.NUMBER.getValue().toString(),
                    "number_precision", 2),
            Map.of(
                    "name", "银进单价",
                    "sign", "silver_price",
                    "secret_level", GoodsColumnSecretLevelEnum.SECRET.getValue().toString(),
                    "type", GoodsColumnTypeEnum.NUMBER.getValue().toString(),
                    "number_precision", 2),
            Map.of(
                    "name", "进工费单价",
                    "sign", "work_price",
                    "secret_level", GoodsColumnSecretLevelEnum.SECRET.getValue().toString(),
                    "type", GoodsColumnTypeEnum.NUMBER.getValue().toString(),
                    "number_precision", 2),
            Map.of(
                    "name", "证书费",
                    "sign", "cert_price",
                    "secret_level", GoodsColumnSecretLevelEnum.NORMAL.getValue().toString(),
                    "type", GoodsColumnTypeEnum.NUMBER.getValue().toString(),
                    "number_precision", 2),
            Map.of(
                    "name", "销售方式",
                    "sign", "sales_type",
                    "secret_level", GoodsColumnSecretLevelEnum.NORMAL.getValue().toString(),
                    "type", GoodsColumnTypeEnum.SELECT.getValue().toString(),
                    "number_precision", 0),
            Map.of(
                    "name", "工费单价",
                    "sign", "sale_work_price",
                    "secret_level", GoodsColumnSecretLevelEnum.NORMAL.getValue().toString(),
                    "type", GoodsColumnTypeEnum.NUMBER.getValue().toString(),
                    "number_precision", 2),
            Map.of(
                    "name", "标签单价",
                    "sign", "tag_price",
                    "secret_level", GoodsColumnSecretLevelEnum.NORMAL.getValue().toString(),
                    "type", GoodsColumnTypeEnum.NUMBER.getValue().toString(),
                    "number_precision", 2),
            Map.of(
                    "name", "所属大类",
                    "sign", "category_id",
                    "secret_level", GoodsColumnSecretLevelEnum.NORMAL.getValue().toString(),
                    "type", GoodsColumnTypeEnum.SELECT.getValue().toString(),
                    "number_precision", 0),
            Map.of(
                    "name", "货品小类",
                    "sign", "subclass_id",
                    "secret_level", GoodsColumnSecretLevelEnum.NORMAL.getValue().toString(),
                    "type", GoodsColumnTypeEnum.SELECT.getValue().toString(),
                    "number_precision", 0),
            Map.of(
                    "name", "成色",
                    "sign", "quality_id",
                    "secret_level", GoodsColumnSecretLevelEnum.NORMAL.getValue().toString(),
                    "type", GoodsColumnTypeEnum.SELECT.getValue().toString(),
                    "number_precision", 0),
            Map.of(
                    "name", "款式",
                    "sign", "style_id",
                    "secret_level", GoodsColumnSecretLevelEnum.NORMAL.getValue().toString(),
                    "type", GoodsColumnTypeEnum.SELECT.getValue().toString(),
                    "number_precision", 0),
            Map.of(
                    "name", "品牌",
                    "sign", "brand_id",
                    "secret_level", GoodsColumnSecretLevelEnum.NORMAL.getValue().toString(),
                    "type", GoodsColumnTypeEnum.SELECT.getValue().toString(),
                    "number_precision", 0),
            Map.of(
                    "name", "圈口",
                    "sign", "circle_size",
                    "secret_level", GoodsColumnSecretLevelEnum.NORMAL.getValue().toString(),
                    "type", GoodsColumnTypeEnum.TEXT.getValue().toString(),
                    "number_precision", 0),
            Map.of(
                    "name", "批次号",
                    "sign", "batch_no",
                    "secret_level", GoodsColumnSecretLevelEnum.NORMAL.getValue().toString(),
                    "type", GoodsColumnTypeEnum.TEXT.getValue().toString(),
                    "number_precision", 0),
            Map.of(
                    "name", "工艺",
                    "sign", "technology_id",
                    "secret_level", GoodsColumnSecretLevelEnum.NORMAL.getValue().toString(),
                    "type", GoodsColumnTypeEnum.SELECT.getValue().toString(),
                    "number_precision", 0),
            Map.of(
                    "name", "证书号",
                    "sign", "cert_no",
                    "secret_level", GoodsColumnSecretLevelEnum.NORMAL.getValue().toString(),
                    "type", GoodsColumnTypeEnum.TEXT.getValue().toString(),
                    "number_precision", 0),
            Map.of(
                    "name", "主石",
                    "sign", "main_stone_id",
                    "secret_level", GoodsColumnSecretLevelEnum.NORMAL.getValue().toString(),
                    "type", GoodsColumnTypeEnum.SELECT.getValue().toString(),
                    "number_precision", 0),
            Map.of(
                    "name", "主石数",
                    "sign", "main_stone_count",
                    "secret_level", GoodsColumnSecretLevelEnum.NORMAL.getValue().toString(),
                    "type", GoodsColumnTypeEnum.NUMBER.getValue().toString(),
                    "number_precision", 0),
            Map.of(
                    "name", "主石重",
                    "sign", "main_stone_weight",
                    "secret_level", GoodsColumnSecretLevelEnum.NORMAL.getValue().toString(),
                    "type", GoodsColumnTypeEnum.NUMBER.getValue().toString(),
                    "number_precision", 3),
            Map.of(
                    "name", "辅石",
                    "sign", "sub_stone_id",
                    "secret_level", GoodsColumnSecretLevelEnum.NORMAL.getValue().toString(),
                    "type", GoodsColumnTypeEnum.SELECT.getValue().toString(),
                    "number_precision", 0),
            Map.of(
                    "name", "辅石数",
                    "sign", "sub_stone_count",
                    "secret_level", GoodsColumnSecretLevelEnum.NORMAL.getValue().toString(),
                    "type", GoodsColumnTypeEnum.NUMBER.getValue().toString(),
                    "number_precision", 0),
            Map.of(
                    "name", "辅石重",
                    "sign", "sub_stone_weight",
                    "secret_level", GoodsColumnSecretLevelEnum.NORMAL.getValue().toString(),
                    "type", GoodsColumnTypeEnum.NUMBER.getValue().toString(),
                    "number_precision", 3)
    // ...
    );
}