package com.xc.boot.common.aspect;

import com.xc.boot.common.annotation.RequestCache;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Arrays;
import java.util.Objects;

/**
 * 请求级缓存切面
 */
@Aspect
@Component
@Slf4j
public class RequestCacheAspect {

    private static final String CACHE_KEY_PREFIX = "request_cache:";

    @Around("@annotation(requestCache)")
    public Object around(ProceedingJoinPoint point, RequestCache requestCache) throws Throwable {
        // 获取当前请求属性
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes == null) {
            return point.proceed();
        }

        // 生成缓存key
        String cacheKey = generateCacheKey(point, requestCache);
        
        // 尝试从缓存中获取结果
        Object cachedResult = requestAttributes.getAttribute(cacheKey, RequestAttributes.SCOPE_REQUEST);
        if (cachedResult != null) {
            log.debug("Cache hit for key: {}", cacheKey);
            return cachedResult;
        }

        // 执行方法并缓存结果
        Object result = point.proceed();
        requestAttributes.setAttribute(cacheKey, result, RequestAttributes.SCOPE_REQUEST);
        log.debug("Cache miss for key: {}, result cached", cacheKey);
        
        return result;
    }

    private String generateCacheKey(ProceedingJoinPoint point, RequestCache requestCache) {
        MethodSignature signature = (MethodSignature) point.getSignature();
        String methodName = signature.getMethod().getName();
        Object[] args = point.getArgs();

        StringBuilder keyBuilder = new StringBuilder(CACHE_KEY_PREFIX);
        if (!requestCache.prefix().isEmpty()) {
            keyBuilder.append(requestCache.prefix()).append(":");
        }
        keyBuilder.append(methodName).append(":");

        if (requestCache.useAllParams()) {
            // 使用所有参数生成key
            keyBuilder.append(Arrays.deepToString(args));
        } else if (args.length > 0) {
            // 只使用第一个参数生成key
            keyBuilder.append(Objects.toString(args[0]));
        }

        return keyBuilder.toString();
    }
} 