package com.xc.boot.common.util;

import com.xc.boot.modules.goods.mapper.GoodsOutcomeMapper;
import com.xc.boot.modules.goods.mapper.GoodsTakeMapper;
import com.xc.boot.modules.goods.model.entity.GoodsOutcomeEntity;
import com.xc.boot.modules.goods.model.entity.GoodsTakeEntity;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.xc.boot.common.exception.BusinessException;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.goods.mapper.GoodsMapper;
import com.xc.boot.modules.goods.model.entity.GoodsEntity;
import com.xc.boot.modules.income.mapper.GoodsIncomeMapper;
import com.xc.boot.modules.income.model.entity.GoodsIncomeEntity;
import com.xc.boot.modules.goods.model.entity.GoodsTransferEntity;
import com.xc.boot.modules.goods.mapper.GoodsTransferMapper;

import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import static com.xc.boot.modules.goods.model.entity.GoodsTransferEntity.*;

/**
 * 单号生成公共封装
 */
@Slf4j
@Component
public class SnUtils implements ApplicationContextAware {
    private static GoodsIncomeMapper goodsIncomeMapper;
    private static GoodsOutcomeMapper goodsOutcomeMapper;
    private static GoodsMapper goodsMapper;
    private static StringRedisTemplate redisTemplate;
    private static ApplicationContext applicationContext;
    private static GoodsTransferMapper goodsTransferMapper;
    private static GoodsTakeMapper goodsTakeMapper;

    /**
     * 缓存前缀
     */
    private static final String CACHE_PREFIX = "generate_sn";

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        SnUtils.applicationContext = applicationContext;

        goodsIncomeMapper = applicationContext.getBean(GoodsIncomeMapper.class);
        goodsOutcomeMapper = applicationContext.getBean(GoodsOutcomeMapper.class);
        goodsMapper = applicationContext.getBean(GoodsMapper.class);
        redisTemplate = applicationContext.getBean(StringRedisTemplate.class);
        goodsTransferMapper = applicationContext.getBean(GoodsTransferMapper.class);
        goodsTakeMapper = applicationContext.getBean(GoodsTakeMapper.class);
    }

    /**
     * 获取公司ID
     */
    private static Long getCompanyId() {
        Long companyId = SecurityUtils.getCompanyId();
        if (companyId == null) {
            throw new BusinessException("获取公司ID失败");
        }
        return companyId;
    }

    /**
     * 生成Redis key
     */
    private static String generateRedisKey(String type, String date) {
        return String.format("%s:%s:%d:%s", CACHE_PREFIX, type, getCompanyId(), date);
    }

    /**
     * 更新Redis计数器
     */
    private static Long updateRedisCounter(String key) {
        Long sequence = redisTemplate.opsForValue().increment(key);
        if (sequence == 1) {
            redisTemplate.expire(key, java.time.Duration.ofDays(7));
        }
        return sequence;
    }

    /**
     * 格式化序号
     */
    private static String formatSequence(Long sequence) {
        return String.format("%05d", sequence);
    }

    /**
     * 生成入库单号
     * 
     * 规则: RK + 年(4位) + 月 + 日 + 5 位顺序数字
     * 
     * @return
     */
    public static String generateIncomeCode() {
        String date = DateUtil.format(DateUtil.date(), "yyyyMMdd");
        String key = generateRedisKey("income", date);
        Long sequence = updateRedisCounter(key);
        return "RK" + date + formatSequence(sequence);
    }

    /**
     * 生成出库单号
     *
     * 规则: CK + 年(4位) + 月 + 日 + 5 位顺序数字
     *
     * @return
     */
    public static String generateOutcomeCode() {
        String date = DateUtil.format(DateUtil.date(), "yyyyMMdd");
        String key = generateRedisKey("outcome", date);
        Long sequence = updateRedisCounter(key);
        return "CK" + date + formatSequence(sequence);
    }

    /**
     * 生成盘点单号
     *
     * 规则: PD + 年(4位) + 月 + 日 + 5 位顺序数字
     *
     * @return
     */
    public static String generateTakeCode() {
        String date = DateUtil.format(DateUtil.date(), "yyyyMMdd");
        String key = generateRedisKey("take", date);
        Long sequence = updateRedisCounter(key);
        return "PD" + date + formatSequence(sequence);
    }

    /**
     * 生成货品条码
     * 
     * 规则: 年(2位) + 月 + 日 + 5 位顺序数字
     * 
     * @return
     */
    public static String generateGoodsSn() {
        String date = DateUtil.format(DateUtil.date(), "yyMMdd");
        String key = generateRedisKey("goods", date);
        Long sequence = updateRedisCounter(key);
        return date + formatSequence(sequence);
    }

    /**
     * 生成调拨单号
     * 规则: DB + 年(4位) + 月 + 日 + 5 位顺序数字
     */
    public static String generateTransferCode() {
        String date = DateUtil.format(DateUtil.date(), "yyyyMMdd");
        String key = generateRedisKey("transfer", date);
        Long sequence = updateRedisCounter(key);
        return "DB" + date + formatSequence(sequence);
    }

    /**
     * 同步数据库中的最大序号到 Redis
     * 
     * 在业务处理完毕后(异常/回滚), 调用此方法, 避免出现单号跳过的问题
     */
    public static void syncMaxSequence() {
        Long companyId = getCompanyId();

        CommonUtils.asyncExecute(() -> {
            try {
                // * 同步入库单号最大序号
                String incomeDate = DateUtil.format(DateUtil.date(), "yyyyMMdd");
                String incomeKey = generateRedisKey("income", incomeDate);
                Long incomeMaxSequence = goodsIncomeMapper.selectOneByQueryAs(
                    QueryWrapper.create()
                        .select(QueryMethods.max(QueryMethods.substring(GoodsIncomeEntity::getIncomeCode, -5)).as("max_sequence"))
                        .where(GoodsIncomeEntity::getCompanyId).eq(companyId)
                        .and(GoodsIncomeEntity::getIncomeCode).likeLeft("RK" + incomeDate),
                    Long.class
                );
                if (incomeMaxSequence == null) {
                    incomeMaxSequence = 0L;
                }
                redisTemplate.opsForValue().set(incomeKey, String.valueOf(incomeMaxSequence));
                redisTemplate.expire(incomeKey, java.time.Duration.ofDays(7));

                // * 同步货品条码最大序号
                String goodsDate = DateUtil.format(DateUtil.date(), "yyMMdd");
                String goodsKey = generateRedisKey("goods", goodsDate);
                Long goodsMaxSequence = goodsMapper.selectOneByQueryAs(
                    QueryWrapper.create()
                        .select(QueryMethods.max(QueryMethods.substring(GoodsEntity::getGoodsSn, -5)).as("max_sequence"))
                        .where(GoodsEntity::getCompanyId).eq(companyId)
                        .and(GoodsEntity::getGoodsSn).likeLeft(goodsDate),
                    Long.class
                );
                if (goodsMaxSequence == null) {
                    goodsMaxSequence = 0L;
                }
                redisTemplate.opsForValue().set(goodsKey, String.valueOf(goodsMaxSequence));
                redisTemplate.expire(goodsKey, java.time.Duration.ofDays(7));

                // * 同步出库单号最大序号
                String outcomeDate = DateUtil.format(DateUtil.date(), "yyyyMMdd");
                String outcomeKey = generateRedisKey("outcome", outcomeDate);
                Long outcomeMaxSequence = goodsOutcomeMapper.selectOneByQueryAs(
                        QueryWrapper.create()
                                .select(QueryMethods.max(QueryMethods.substring(GoodsOutcomeEntity::getOutcomeCode, -5)).as("max_sequence"))
                                .where(GoodsOutcomeEntity::getCompanyId).eq(companyId)
                                .and(GoodsOutcomeEntity::getOutcomeCode).likeLeft("CK" + outcomeDate),
                        Long.class
                );
                if (outcomeMaxSequence == null) {
                    outcomeMaxSequence = 0L;
                }
                redisTemplate.opsForValue().set(outcomeKey, String.valueOf(outcomeMaxSequence));
                redisTemplate.expire(outcomeKey, java.time.Duration.ofDays(7));

                // * 同步调拨单号最大序号
                String transferDate = DateUtil.format(DateUtil.date(), "yyyyMMdd");
                String transferKey = generateRedisKey("transfer", transferDate);
                Long transferMaxSequence = goodsTransferMapper.selectOneByQueryAs(
                        QueryWrapper.create()
                                .select(QueryMethods.max(QueryMethods.substring(GoodsTransferEntity::getTransferSn, -5)).as("max_sequence"))
                                .where(GoodsTransferEntity::getCompanyId).eq(companyId)
                                .and(GoodsTransferEntity::getTransferSn).likeLeft("DB" + transferDate),
                        Long.class
                );
                if (transferMaxSequence == null) {
                    transferMaxSequence = 0L;
                }
                redisTemplate.opsForValue().set(transferKey, String.valueOf(transferMaxSequence));
                redisTemplate.expire(transferKey, java.time.Duration.ofDays(7));

                // * 同步盘点单号最大序号
                String takeDate = DateUtil.format(DateUtil.date(), "yyyyMMdd");
                String takeKey = generateRedisKey("take", takeDate);
                Long takeMaxSequence = goodsTakeMapper.selectOneByQueryAs(
                        QueryWrapper.create()
                                .select(QueryMethods.max(QueryMethods.substring(GoodsTakeEntity::getTakeCode, -5)).as("max_sequence"))
                                .where(GoodsTakeEntity::getCompanyId).eq(companyId)
                                .and(GoodsTakeEntity::getTakeCode).likeLeft("PD" + takeDate),
                        Long.class
                );
                if (takeMaxSequence == null) {
                    takeMaxSequence = 0L;
                }
                redisTemplate.opsForValue().set(takeKey, String.valueOf(takeMaxSequence));
                redisTemplate.expire(takeKey, java.time.Duration.ofDays(7));
            } catch (Exception e) {
                log.error("同步最大序号失败", e);
            }
        });
    }
}
