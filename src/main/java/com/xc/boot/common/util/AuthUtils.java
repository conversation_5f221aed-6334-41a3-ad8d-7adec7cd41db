package com.xc.boot.common.util;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import com.mybatisflex.core.query.QueryWrapper;
import com.xc.boot.core.security.extension.MockAuthenticationToken;
import com.xc.boot.core.security.model.SysUserDetails;
import com.xc.boot.system.mapper.CompanyMapper;
import com.xc.boot.system.mapper.UserMapper;
import com.xc.boot.system.model.entity.CompanyEntity;
import com.xc.boot.system.model.entity.SysUserEntity;

/**
 * 公共认证工具类
 */
@Component
public class AuthUtils implements ApplicationContextAware {
    private static UserMapper userMapper;
    private static CompanyMapper companyMapper;
    private static ApplicationContext applicationContext;
    private static AuthenticationManager authenticationManager;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        AuthUtils.applicationContext = applicationContext;

        userMapper = applicationContext.getBean(UserMapper.class);
        companyMapper = applicationContext.getBean(CompanyMapper.class);
        authenticationManager = applicationContext.getBean(AuthenticationManager.class);
    }

    private static void mock(SysUserEntity user, CompanyEntity company) {
        SysUserDetails userDetails = new SysUserDetails(user);
        userDetails.setCompany(company);

        MockAuthenticationToken token = new MockAuthenticationToken(userDetails, userDetails.getAuthorities());
        Authentication authentication = authenticationManager.authenticate(token);
        SecurityContextHolder.getContext().setAuthentication(authentication);
    }

    public static void mockUser(Integer userId) {
        SysUserEntity user = userMapper.selectOneById(userId);
        CompanyEntity company = companyMapper.selectOneById(user.getCompanyId());

        mock(user, company);
    }

    public static void mockUserByCompanyId(Integer companyId) {
        CompanyEntity company = companyMapper.selectOneById(companyId);
        SysUserEntity user = userMapper.selectOneByQuery(QueryWrapper.create()
                .from(SysUserEntity.class)
                .where(SysUserEntity::getCompanyId).eq(companyId)
                .where(SysUserEntity::getMainFlag).eq(1));
        mock(user, company);
    }
}
