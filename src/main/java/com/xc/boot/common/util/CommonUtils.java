package com.xc.boot.common.util;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.query.QueryMethods;
import com.xc.boot.common.exception.BusinessException;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.modules.merchant.mapper.GoodsColumnMapper;
import com.xc.boot.modules.merchant.model.entity.GoodsColumnEntity;
import com.xc.boot.system.mapper.FileMapper;
import com.xc.boot.system.model.entity.FileEntity;
import com.xc.boot.system.model.vo.CompanySettingsVO;
import com.xc.boot.system.service.CompanySettingsService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 涉及业务逻辑的公共工具类
 */
@Component
public class CommonUtils implements ApplicationContextAware {
    private static FileMapper fileMapper;
    private static AsyncTaskExecutor asyncTaskExecutor;
    private static ApplicationContext applicationContext;
    private static CompanySettingsService companySettingsService;
    private static GoodsColumnMapper goodsColumnMapper;

    /**
     * 货品字段列表线程变量
     */
    private static final ThreadLocal<List<GoodsColumnEntity>> GOODS_COLUMNS = new ThreadLocal<>();

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        CommonUtils.applicationContext = applicationContext;

        fileMapper = applicationContext.getBean(FileMapper.class);
        asyncTaskExecutor = applicationContext.getBean(AsyncTaskExecutor.class);
        companySettingsService = applicationContext.getBean(CompanySettingsService.class);
        goodsColumnMapper = applicationContext.getBean(GoodsColumnMapper.class);
    }

    /**
     * 获取货品字段列表
     * 
     * @return 货品字段列表
     */
    public static List<GoodsColumnEntity> getGoodsColumns() {
        // 如果线程变量中已有数据，直接返回
        List<GoodsColumnEntity> columns = GOODS_COLUMNS.get();
        if (columns != null) {
            return columns;
        }

        // 查询数据库
        columns = goodsColumnMapper.selectListByQuery(
            QueryWrapper.create()
                .where(GoodsColumnEntity::getCompanyId).eq(SecurityUtils.getCompanyId())
        );

        // 存入线程变量
        GOODS_COLUMNS.set(columns);
        return columns;
    }

    /**
     * 根据sign获取货品字段
     * @param sign
     * @return
     */
    public static GoodsColumnEntity getGoodsColumnsBySign(String sign) {
        return getGoodsColumns().stream().filter(column -> column.getSign().equals(sign)).findFirst().orElse(null);
    }

    /**
     * 清理所有线程变量
     */
    public static void clearThreadLocal() {
        // 清理货品字段线程变量
        GOODS_COLUMNS.remove();
        // 后续可以在这里添加其他线程变量的清理逻辑
    }

    /**
     * 异步执行任务
     * 
     * @param runnable 任务
     */
    public static void asyncExecute(Runnable runnable) {
        asyncTaskExecutor.execute(runnable);
    }

    /**
     * 异步更新文件状态
     *
     * @param fileId 文件id
     * @param status 状态
     */
    public static void updateFileStatus(Long fileId, Integer status) {
        if (fileId == null) {
            return;
        }
        asyncExecute(() -> {
            FileEntity file = new FileEntity();
            file.setId(fileId);
            file.setStatus(status);
            fileMapper.update(file);
        });
    }

    /**
     * 批量异步更新文件状态
     *
     * @param fileIds 文件id列表
     * @param status 状态
     */
    public static void batchUpdateFileStatus(List<Long> fileIds, Integer status) {
        if (fileIds == null || fileIds.isEmpty()) {
            return;
        }
        asyncExecute(() -> {
            FileEntity file = new FileEntity();
            file.setStatus(status);
            fileMapper.updateByQuery(file, QueryWrapper.create()
                .where(FileEntity::getId).in(fileIds));
        });
    }

    public static boolean isProd() {
        return ContextUtils.isProd();
    }

    /**
     * 更新API文档到Apifox
     * 
     * @return 更新结果的格式化字符串
     */
    public static String updateApiDoc() {
        try {
            // 定义API相关常量
            final String APIFOX_API_VERSION = "2024-03-28";
            final String PROJECT_ID = "6531359";
            final String ACCESS_TOKEN = "APS-ij8JeMpE5xcw1MW29mcuf34U8UcnVkmU";
            final String OPENAPI_URL = "https://gold-dev.fbzs.net/v3/api-docs/Gold";
            final String APIFOX_API_BASE_URL = "https://api.apifox.com/v1/projects/";

            RestTemplate restTemplate = new RestTemplate();
            String url = APIFOX_API_BASE_URL + PROJECT_ID + "/import-openapi";

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("X-Apifox-Api-Version", APIFOX_API_VERSION);
            headers.set("Authorization", "Bearer " + ACCESS_TOKEN);

            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();

            // 构建input对象
            Map<String, Object> input = new HashMap<>();
            input.put("url", OPENAPI_URL);

            // 添加basicAuth认证信息
            Map<String, String> basicAuth = new HashMap<>();
            basicAuth.put("username", "xc.api");
            basicAuth.put("password", "WIxvxomRlgN4Y8Q");
            input.put("basicAuth", basicAuth);

            requestBody.put("input", input);

            // 设置options
            Map<String, Object> options = new HashMap<>();
            options.put("targetEndpointFolderId", 0); // 使用Root目录
            options.put("targetSchemaFolderId", 0); // 使用Root目录
            options.put("endpointOverwriteBehavior", "OVERWRITE_EXISTING");
            options.put("schemaOverwriteBehavior", "OVERWRITE_EXISTING");
            options.put("updateFolderOfChangedEndpoint", false);
            options.put("prependBasePath", false);
            requestBody.put("options", options);

            // 发送请求
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);
            ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);

            // 解析响应并格式化
            ObjectMapper mapper = new ObjectMapper();
            JsonNode rootNode = mapper.readTree(response.getBody());

            // 检查是否有错误
            if (rootNode.has("success") && !rootNode.get("success").asBoolean()) {
                String errorMessage = rootNode.has("errorMessage") ? rootNode.get("errorMessage").asText() : "未知错误";
                return "更新API文档失败：" + errorMessage;
            }

            JsonNode dataNode = rootNode.get("data");
            JsonNode countersNode = dataNode.get("counters");

            StringBuilder result = new StringBuilder();
            result.append("<b>API文档更新结果</b>：\n\n");
            result.append("新增的接口数: ").append(countersNode.get("endpointCreated").asInt()).append("\n");
            result.append("修改的接口数: ").append(countersNode.get("endpointUpdated").asInt()).append("\n");
            result.append("导入出错接口数: ").append(countersNode.get("endpointFailed").asInt()).append("\n");
            result.append("忽略的接口数: ").append(countersNode.get("endpointIgnored").asInt()).append("\n");
            result.append("新增的数据模型数: ").append(countersNode.get("schemaCreated").asInt()).append("\n");
            result.append("修改的数据模型数: ").append(countersNode.get("schemaUpdated").asInt()).append("\n");
            result.append("导入出错数据模型数: ").append(countersNode.get("schemaFailed").asInt()).append("\n");
            result.append("忽略的数据模型数: ").append(countersNode.get("schemaIgnored").asInt()).append("\n");

            // 如果有错误信息，添加到结果中
            JsonNode errorsNode = rootNode.get("errors");
            if (errorsNode != null && errorsNode.isArray() && errorsNode.size() > 0) {
                result.append("\n错误信息：\n");
                for (JsonNode error : errorsNode) {
                    result.append("- ").append(error.get("message").asText()).append("\n");
                }
            }

            return result.toString();
        } catch (Exception e) {
            return "更新API文档时发生错误：" + e.getMessage();
        }
    }

    /**
     * 获取商户设置
     * 
     * @param companyId 商户ID，如果为null则使用当前登录用户的商户ID
     * @return 商户设置，如果不存在则返回null
     */
    public static CompanySettingsVO getCompanySettings(Long companyId) {
        // 如果未传入商户ID，则使用当前登录用户的商户ID
        Long targetCompanyId = Optional.ofNullable(companyId)
                .orElseGet(SecurityUtils::getCompanyId);

        if (targetCompanyId == null) {
            return null;
        }

        // 从缓存中获取商户设置
        return companySettingsService.getSettings();
    }

    /**
     * 获取最大打印数量
     */
    public static int getMaxPrintSize() {
        return 5000;
    }

    /**
     * 获取最大导出数量
     */
    public static int getMaxExportSize() {
        return 5000;
    }

    /**
     * 抛出业务异常
     * 
     * @param message
     */
    public static void abort(String message) {
        throw new BusinessException(message);
    }

    /**
     * 如果条件为真，抛出业务异常
     * 
     * @param condition 条件
     * @param message   异常消息
     */
    public static void abortIf(boolean condition, String message) {
        if (condition) {
            abort(message);
        }
    }
}
