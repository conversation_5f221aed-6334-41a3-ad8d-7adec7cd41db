package com.xc.boot.system.service.impl;

import cn.hutool.core.util.StrUtil;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.system.mapper.UserNoticeMapper;
import com.xc.boot.system.model.entity.SysNoticeEntity;
import com.xc.boot.system.model.entity.SysUserEntity;
import com.xc.boot.system.model.entity.SysUserNoticeEntity;
import com.xc.boot.system.model.query.NoticePageQuery;
import com.xc.boot.system.model.vo.NoticePageVO;
import com.xc.boot.system.model.vo.UserNoticePageVO;
import com.xc.boot.system.service.UserNoticeService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 用户公告状态服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-28 16:56
 */
@Service
@RequiredArgsConstructor
public class UserNoticeServiceImpl extends ServiceImpl<UserNoticeMapper, SysUserNoticeEntity> implements UserNoticeService {

    private final UserNoticeMapper userNoticeMapper;

    /**
     * 全部标记为已读
     *
     * @return 是否成功
     */
    @Override
    public boolean readAll() {
        Long userId = SecurityUtils.getUserId();
        return this.update(
                new SysUserNoticeEntity().setIsRead(1),
                QueryWrapper.create()
                        .eq(SysUserNoticeEntity::getUserId, userId)
                        .eq(SysUserNoticeEntity::getIsRead, 0)
        );
    }

    /**
     * 我的通知公告分页列表
     *
     * @param page        分页对象
     * @param queryParams 查询参数
     * @return 通知公告分页列表
     */
    @Override
    public Page<UserNoticePageVO> getMyNoticePage(NoticePageQuery queryParams) {
        QueryWrapper query = QueryWrapper.create().from(QueryWrapper.class)
                .select(
                        QueryMethods.column(SysNoticeEntity::getId),
                        QueryMethods.column(SysNoticeEntity::getTitle),
                        QueryMethods.column(SysNoticeEntity::getPublishTime),
                        QueryMethods.column(SysNoticeEntity::getForcePopup),
                        QueryMethods.column(SysNoticeEntity::getSort),
                        QueryMethods.column(SysUserEntity::getNickname).as(UserNoticePageVO::getPublisherName),
                        QueryMethods.column(SysUserNoticeEntity::getIsRead)
                )
                .leftJoin(SysNoticeEntity.class).on(q->q.and(SysNoticeEntity::getId).eq(SysUserNoticeEntity::getNoticeId).and(SysNoticeEntity::getPublishStatus).eq(1))
                .leftJoin(SysUserEntity.class).on(q->q.and(SysUserEntity::getId).eq(SysNoticeEntity::getPublisherId))
                .and(SysUserNoticeEntity::getUserId).eq(queryParams.getUserId())
                .and(SysNoticeEntity::getTitle).like(queryParams.getTitle(), StrUtil.isNotBlank(queryParams.getTitle()))
                .orderBy(SysNoticeEntity::getPublishTime).desc()
                .orderBy(SysNoticeEntity::getSort).desc()
                .orderBy(SysUserNoticeEntity::getCreatedAt).desc();
        return this.mapper.paginateWithRelationsAs(
                new Page<>(queryParams.getPageNum(), queryParams.getPageSize()),
                query,
                UserNoticePageVO.class
        );
    }


}
