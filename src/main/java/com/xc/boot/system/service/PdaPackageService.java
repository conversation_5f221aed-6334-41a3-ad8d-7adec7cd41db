package com.xc.boot.system.service;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import com.xc.boot.system.model.entity.PdaPackageEntity;
import com.xc.boot.system.model.form.PdaPackageForm;
import com.xc.boot.system.model.query.PdaPackagePageQuery;
import com.xc.boot.system.model.vo.PdaPackageVo;

import java.util.List;

/**
 * PDA包管理服务接口
 */
public interface PdaPackageService extends IService<PdaPackageEntity> {

    /**
     * 分页查询PDA包列表
     */
    Page<PdaPackageVo> page(PdaPackagePageQuery query);

    /**
     * 创建PDA包
     */
    void create(PdaPackageForm form);

    /**
     * 更新PDA包
     */
    void update(PdaPackageForm form);

    /**
     * 删除PDA包
     */
    void delete(Long id);

    /**
     * 根据PDA类型获取当前生效的最新包
     */
    PdaPackageVo getLatestActivePackageByType(String type, String version);
} 