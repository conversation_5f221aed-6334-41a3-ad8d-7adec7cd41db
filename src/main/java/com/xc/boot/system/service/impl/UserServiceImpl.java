package com.xc.boot.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.xc.boot.common.constant.RedisConstants;
import com.xc.boot.common.constant.SecurityConstants;
import com.xc.boot.common.enums.SideEnum;
import com.xc.boot.common.exception.BusinessException;
import com.xc.boot.common.model.Option;
import com.xc.boot.common.result.ResultCode;
import com.xc.boot.common.util.CommonUtils;
import com.xc.boot.common.util.ContextUtils;
import com.xc.boot.common.util.IPUtils;
import com.xc.boot.common.util.OpLogUtils;
import com.xc.boot.common.util.excel.ExcelUtil;
import com.xc.boot.common.util.listFill.ListFillService;
import com.xc.boot.common.util.listFill.ListFillUtil;
import com.xc.boot.core.security.model.SysUserDetails;
import com.xc.boot.core.security.service.PermissionService;
import com.xc.boot.core.security.util.SecurityUtils;
import com.xc.boot.shared.auth.service.TokenService;
import com.xc.boot.shared.mail.service.MailService;
import com.xc.boot.shared.sms.service.SmsService;
import com.xc.boot.system.converter.UserConverter;
import com.xc.boot.system.mapper.*;
import com.xc.boot.system.model.bo.UserBO;
import com.xc.boot.system.model.dto.UserAuthInfo;
import com.xc.boot.system.model.dto.UserExportDTO;
import com.xc.boot.system.model.entity.*;
import com.xc.boot.system.model.form.PasswordChangeForm;
import com.xc.boot.system.model.form.UserForm;
import com.xc.boot.system.model.form.UserProfileForm;
import com.xc.boot.system.model.form.UserUpdateForm;
import com.xc.boot.system.model.query.UserPageQuery;
import com.xc.boot.system.model.vo.*;
import com.xc.boot.system.service.RoleService;
import com.xc.boot.system.service.UserService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.xc.boot.system.model.entity.table.CompanyTableDef.COMPANY;
import static com.xc.boot.system.model.entity.table.MerchantTableDef.MERCHANT;
import static com.xc.boot.system.model.entity.table.SysUserMerchantTableDef.SYS_USER_MERCHANT;
import static com.xc.boot.system.model.entity.table.SysUserRoleTableDef.SYS_USER_ROLE;
import static com.xc.boot.system.model.entity.table.SysUserTableDef.SYS_USER;
import static com.xc.boot.system.model.entity.table.VerifyCodeTableDef.VERIFY_CODE;


/**
 * 用户业务实现类
 *
 * <AUTHOR>
 * @since 2022/1/14
 */
@Service
@RequiredArgsConstructor
public class UserServiceImpl extends ServiceImpl<UserMapper, SysUserEntity> implements UserService {

    private final PasswordEncoder passwordEncoder;

    private final RoleService roleService;

    private final PermissionService permissionService;

    private final SmsService smsService;

    private final MailService mailService;

    private final StringRedisTemplate redisTemplate;

    private final TokenService tokenService;

    private final UserConverter userConverter;

    private final ListFillService listFillService;

    private final UserRoleMapper userRoleMapper;

    private final UserMerchantMapper userMerchantMapper;

    private final RedissonClient redissonClient;

    private final VerifyCodeMapper verifyCodeMapper;

    private final CompanyMapper companyMapper;

    private final MerchantMapper merchantMapper;

    /**
     * 获取用户分页列表
     *
     * @param queryParams 查询参数
     * @return {@link Page<UserPageVO>} 用户分页列表
     */
    @Override
    public Page<UserPageVO> getUserPage(UserPageQuery queryParams) {

        // 参数构建
        QueryWrapper query = buildQuery(queryParams);
        query.orderBy(SysUserEntity::getId).desc();

        // 导出
        if (queryParams.getExport().equals(1)) {
            exportUsers(query, queryParams);
            return new Page<>();
        }
        query.select(
                QueryMethods.column(SysUserEntity::getId),
                QueryMethods.column(SysUserEntity::getAvatar),
                QueryMethods.column(SysUserEntity::getAvatarId),
                QueryMethods.column(SysUserEntity::getUsername),
                QueryMethods.column(SysUserEntity::getNickname),
                QueryMethods.column(SysUserEntity::getGender),
                QueryMethods.column(SysUserEntity::getStatus),
                QueryMethods.column(SysUserEntity::getSecret),
                QueryMethods.column(SysUserEntity::getMainFlag),
                QueryMethods.column(SysUserEntity::getCreatedAt),
                QueryMethods.column(SysUserEntity::getUpdatedAt)
        );
        int pageSize = queryParams.getPageSize();
        int pageNum = queryParams.getPageNum();
        // 打印
        if (queryParams.getPrint().equals(1)) {
            pageNum = 1;
            pageSize = CommonUtils.getMaxPrintSize();
            long count = this.mapper.selectCountByQuery(query);
            Assert.isTrue(count <= CommonUtils.getMaxPrintSize(), String.format("打印数据条数超出最大限制%d条", CommonUtils.getMaxPrintSize()));
            if (CollectionUtil.isNotEmpty(queryParams.getIds())) {
                query.where(SYS_USER.ID.in(queryParams.getIds()));
            }
        }
        Page<UserPageVO> page = this.mapper.paginateAs(pageNum, pageSize, query, UserPageVO.class);
        List<UserPageVO> records = page.getRecords();
        if (records.isEmpty()) {
            return page;
        }
        Set<Long> ids = records.stream().map(UserPageVO::getId).collect(Collectors.toSet());
        ListFillUtil.of(records)
                .build(listFillService::getUserRoleVosByUserId, ids,"id", "roles")
                .build(listFillService::getUserMerchantVosByUserId, ids,"id", "merchants")
                .peek(record -> {
                    UserPageVO vo = (UserPageVO) record;
                    if (vo.getMainFlag()) {
                        List<UserRoleVo> userRoleVos = Optional.ofNullable(vo.getRoles()).orElse(new ArrayList<>());
                        userRoleVos.add(new UserRoleVo().setName("主账号"));
                        vo.setRoles(userRoleVos);
                        List<UserMerchantVo> userMerchantVos = Optional.ofNullable(vo.getMerchants()).orElse(new ArrayList<>());
                        userMerchantVos.add(new UserMerchantVo().setName("主账号"));
                        vo.setMerchants(userMerchantVos);
                    }
                })
                .handle();
        return page;
    }

    private QueryWrapper buildQuery(UserPageQuery queryParams) {
        SysUserDetails sysUserDetails = SecurityUtils.getUser().orElse(new SysUserDetails());
        // 查询数据
        QueryWrapper query = QueryWrapper.create().from(SysUserEntity.class);
        query.where(SysUserEntity::getCompanyId).eq(sysUserDetails.getCompanyId());

        // 姓名
        query.where(SysUserEntity::getNickname).like(queryParams.getNickname(), StringUtils.isNotBlank(queryParams.getNickname()));
        // 手机号
        query.where(SysUserEntity::getUsername).eq(queryParams.getUsername(), StringUtils.isNotBlank(queryParams.getUsername()));
        // 状态
        query.where(SysUserEntity::getStatus).eq(queryParams.getStatus(), queryParams.getStatus() != null);
        if (Objects.nonNull(queryParams.getTimeRange())) {
            // 创建时间开始时间
            query.where(SysUserEntity::getCreatedAt).ge(queryParams.getTimeRange()[0]);
            // 创建时间结束时间
            query.where(SysUserEntity::getCreatedAt).le(queryParams.getTimeRange()[1]);
        }

        // 角色
        if (queryParams.getRoleId() != null) {
            query.where(QueryMethods.exists(QueryWrapper.create().from(SYS_USER_ROLE)
                    .where(SYS_USER_ROLE.USER_ID.eq(SYS_USER.ID))
                    .where(SYS_USER_ROLE.ROLE_ID.eq(queryParams.getRoleId()))));
        }
        // 门店
        if (queryParams.getMerchantId() != null) {
            query.where(QueryMethods.exists(QueryWrapper.create().from(SYS_USER_MERCHANT)
                    .where(SYS_USER_MERCHANT.USER_ID.eq(SYS_USER.ID))
                    .where(SYS_USER_MERCHANT.MERCHANT_ID.eq(queryParams.getMerchantId()))));
        }
        return query;
    }

    @Override
    @Transactional
    public boolean update(UserUpdateForm updateForm) {
        SysUserEntity sysUserEntity = this.mapper.selectOneById(updateForm.getId());
        Assert.notNull(sysUserEntity, ResultCode.USER_NOT_EXIST.getMsg());

        // 记录操作日志
        OpLogUtils.appendOpLog("用户管理-编辑用户", "编辑用户: " + sysUserEntity.getUsername(),
            Map.of("修改前", sysUserEntity, "修改后", updateForm));

        // 头像文件状态处理
        if (Objects.nonNull(sysUserEntity.getAvatarId()) && !sysUserEntity.getAvatarId().equals(updateForm.getAvatarId())) {
            CommonUtils.updateFileStatus(sysUserEntity.getAvatarId(), 0);
        }
        if (Objects.nonNull(updateForm.getAvatarId())) {
            CommonUtils.updateFileStatus(updateForm.getAvatarId(), 1);
        }

        // 用户名修改
        if (!sysUserEntity.getUsername().equals(updateForm.getUsername())) {
            RLock lock = redissonClient.getLock(SecurityConstants.USERNAME_VERIFY_KEY + sysUserEntity.getUsername());
            try {
                lock.lock(3, TimeUnit.SECONDS);
                boolean usable = usernameUsable(updateForm.getUsername(), updateForm.getId(), sysUserEntity.getCompanyId());
                if (!usable) {
                    throw new BusinessException(ResultCode.USERNAME_USED);
                }

                BeanUtil.copyProperties(updateForm, sysUserEntity);
                this.mapper.update(sysUserEntity, false);
            }finally {
                lock.unlock();
            }
        }else {
            // 用户名未修改
            sysUserEntity.setAvatar(updateForm.getAvatar());
            sysUserEntity.setAvatarId(updateForm.getAvatarId());
            sysUserEntity.setNickname(updateForm.getNickname());
            sysUserEntity.setGender(updateForm.getGender());
            sysUserEntity.setStatus(updateForm.getStatus());
            sysUserEntity.setSecret(updateForm.getSecret());
            this.mapper.update(sysUserEntity, false);
        }

        // 角色修改
        userRoleMapper.deleteByQuery(QueryWrapper.create().eq(SysUserRoleEntity::getUserId, sysUserEntity.getId()));
        List<SysUserRoleEntity> roles = updateForm.getRoles().stream()
                .map(role -> new SysUserRoleEntity().setUserId(sysUserEntity.getId()).setRoleId(role))
                .toList();
        userRoleMapper.insertBatch(roles);

        // 门店修改
        userMerchantMapper.deleteByQuery(QueryWrapper.create().eq(SysUserMerchantEntity::getUserId, sysUserEntity.getId()));
        List<SysUserMerchantEntity> merchants = updateForm.getMerchants().stream()
                .map(merchant -> new SysUserMerchantEntity().setUserId(sysUserEntity.getId()).setMerchantId(merchant))
                .toList();
        userMerchantMapper.insertBatch(merchants);

        // 禁用用户时 直接移除token
        if (sysUserEntity.getStatus().equals(0)) {
            tokenService.blacklistToken(sysUserEntity.getId());
            return true;
        }

        // 修改用户信息后刷新令牌状态
        for (SideEnum sideEnum : SideEnum.values()) {
            SysUserDetails userDetails = tokenService.getTokenById(sysUserEntity.getId().toString(), sideEnum.getValue());
            if (userDetails != null) {
                userDetails.setAvatar(updateForm.getAvatar());
                userDetails.setAvatarId(updateForm.getAvatarId());
                userDetails.setNickname(updateForm.getNickname());
                userDetails.setUsername(updateForm.getUsername());
                userDetails.setGender(updateForm.getGender());
                userDetails.setShowSecret(updateForm.getSecret().equals(1));
                tokenService.refreshToken(updateForm.getId().toString(), userDetails);
            }
        }
        return true;
    }

    /**
     * 新增用户
     * @param form 用户表单对象
     * @return
     */
    @Override
    public boolean saveUser(UserUpdateForm form) {
        Long companyId = SecurityUtils.getCompanyId();
        SysUserEntity sysUserEntity = BeanUtil.copyProperties(form, SysUserEntity.class);
        RLock lock = redissonClient.getLock(SecurityConstants.USERNAME_VERIFY_KEY + sysUserEntity.getUsername());
        try {
            lock.lock(3, TimeUnit.SECONDS);
            boolean usable = usernameUsable(form.getUsername(), 0L, companyId);
            if (!usable) {
                throw new BusinessException(ResultCode.USERNAME_USED);
            }
            // 设置密码加密
            String pwd = passwordEncoder.encode(form.getPassword());
            sysUserEntity.setPassword(pwd);

            sysUserEntity.setCompanyId(companyId);
            // 新增用户
            boolean result = this.save(sysUserEntity);
            // 如果有头像 设置为使用状态
            if (Objects.nonNull(sysUserEntity.getAvatarId())) {
                CommonUtils.updateFileStatus(sysUserEntity.getAvatarId(), 1);
            }
            List<SysUserRoleEntity> roles = form.getRoles().stream()
                    .map(role -> new SysUserRoleEntity().setUserId(sysUserEntity.getId()).setRoleId(role))
                    .toList();
            userRoleMapper.insertBatch(roles);
            List<SysUserMerchantEntity> merchants = form.getMerchants().stream()
                    .map(merchant -> new SysUserMerchantEntity().setUserId(sysUserEntity.getId()).setMerchantId(merchant))
                    .toList();
            userMerchantMapper.insertBatch(merchants);

            // 记录操作日志
            OpLogUtils.appendOpLog("用户管理-新增用户", "新增用户: " + form.getUsername(), form);

            return result;
        }finally {
            lock.unlock();
        }
    }

    /**
     * 判断用户名是否可用
     * @param username 待校验用户名
     * @param id 如果与当前使用该username的用户id相同，则可以使用
     * @param companyId 如果与当前使用该username的商家id相同，则可以使用
     * @return
     */
    private boolean usernameUsable(String username, Long id, Long companyId) {
        SysUserEntity userEntity = this.mapper.selectOneByQuery(QueryWrapper.create()
                .where(SysUserEntity::getUsername).eq(username));
        CompanyEntity companyEntity = companyMapper.selectOneByQuery(QueryWrapper.create()
                .where(COMPANY.PHONE.eq(username)));
        return (Objects.isNull(userEntity) || userEntity.getId().equals(id)) && (Objects.isNull(companyEntity) || companyEntity.getId().equals(companyId));
    }

    /**
     * 更新登录用户信息
     * @param userForm 用户表单对象
     * @return
     */
    @Override
    @Transactional
    public boolean updateMe(UserForm userForm) {
        SysUserDetails userDetails = SecurityUtils.getUser().orElse(new SysUserDetails());
        Map<String, String> opLogMap = new HashMap<>();
        opLogMap.put("修改前", JSONUtil.toJsonStr(userDetails));
        // 头像文件更新
        if (Objects.nonNull(userDetails.getAvatarId()) && !userDetails.getAvatarId().equals(userForm.getAvatarId())) {
            // 如果头像有更新，原文件设置为未使用
            CommonUtils.updateFileStatus(userDetails.getAvatarId(), 0);
            // 将新头像设为启用
            if (Objects.nonNull(userForm.getAvatarId())) {
                CommonUtils.updateFileStatus(userForm.getAvatarId(), 1);
            }
        }
        updateChain().from(SysUserEntity.class)
                .set(SysUserEntity::getAvatarId, userForm.getAvatarId())
                .set(SysUserEntity::getAvatar, userForm.getAvatar())
                .set(SysUserEntity::getNickname, userForm.getNickname())
                .set(SysUserEntity::getGender, userForm.getGender())
                .where(SysUserEntity::getId).eq(userForm.getId())
                .update();
        userDetails.setAvatar(userForm.getAvatar());
        userDetails.setAvatarId(userForm.getAvatarId());
        userDetails.setNickname(userForm.getNickname());
        userDetails.setGender(userForm.getGender());
        opLogMap.put("修改后", JSONUtil.toJsonStr(userDetails));
        OpLogUtils.appendOpLog("用户管理-修改个人信息", "修改个人信息", opLogMap);
        // 刷新缓存信息
        for (SideEnum value : SideEnum.values()) {
            userDetails.setSideCode(value.getValue());
            tokenService.refreshToken(userDetails.getUserId().toString(), userDetails);
        }
        return true;
    }

    /**
     * 删除用户
     * @param idsStr 用户ID，多个以英文逗号(,)分割
     * @return true|false
     */
    @Override
    @Transactional
    public boolean deleteUsers(String idsStr) {
        Assert.isTrue(StrUtil.isNotBlank(idsStr), "删除的用户数据为空");
        // 逻辑删除
        Set<Long> ids = Arrays.stream(idsStr.split(","))
                .map(Long::parseLong)
                .collect(Collectors.toSet());
        // 检查，不允许删除当前登录账号、不允许删除角色为ROOT的账号
        if (ids.contains(SecurityUtils.getUserId())) {
            throw new BusinessException("不允许删除当前登录账号!");
        }
        long count = this.mapper.selectCountByQuery(QueryWrapper.create()
                .where(SYS_USER.ID.in(ids))
                .where(SYS_USER.MAIN_FLAG.eq(1)));
        Assert.isTrue(count == 0, "不允许删除主账号!");
        boolean result = this.removeByIds(ids);
        // 成功后删除用户对应的token
        if (result) {
            for (Long id : ids) {
                tokenService.blacklistToken(id);
            }
        }

        // 记录操作日志
        OpLogUtils.appendOpLog("用户管理-删除用户", "删除用户IDs: " + idsStr,
            Map.of("删除的用户", this.listByIds(Arrays.asList(idsStr.split(",")))));

        return result;
    }

    /**
     * 根据用户名获取认证信息
     *
     * @param username 用户名
     * @return 用户认证信息 {@link UserAuthInfo}
     */
    @Override
    public UserAuthInfo getUserAuthInfo(String username) {
        UserAuthInfo userAuthInfo = this.mapper.selectOneWithRelationsByQueryAs(
                QueryWrapper.create().from(SysUserEntity.class)
                        .select(QueryMethods.column(SysUserEntity::getId).as(UserAuthInfo::getUserId))
                        .select(QueryMethods.column(SysUserEntity::getUsername).as(UserAuthInfo::getUsername))
                        .select(QueryMethods.column(SysUserEntity::getNickname).as(UserAuthInfo::getNickname))
                        .select(QueryMethods.column(SysUserEntity::getPassword).as(UserAuthInfo::getPassword))
                        .select(QueryMethods.column(SysUserEntity::getGender).as(UserAuthInfo::getGender))
                        .select(QueryMethods.column(SysUserEntity::getAvatar).as(UserAuthInfo::getAvatar))
                        .select(QueryMethods.column(SysUserEntity::getMainFlag).as(UserAuthInfo::getMainFlag))
                        .select(QueryMethods.column(SysUserEntity::getAvatarId).as(UserAuthInfo::getAvatarId))
                        .select(QueryMethods.column(SysUserEntity::getStatus).as(UserAuthInfo::getStatus))
                        .select(QueryMethods.column(SysUserEntity::getCompanyId).as(UserAuthInfo::getCompanyId))
                        .select(QueryMethods.column(SysUserEntity::getSecret).as(UserAuthInfo::getSecret))
                        .select(QueryMethods.column(SysUserEntity::getCreatedAt).as(UserAuthInfo::getCreatedAt))
                        .and(SysUserEntity::getUsername).eq(username),
                UserAuthInfo.class
        );
        return userAuthInfo;
    }


    /**
     * 根据 openid 获取用户认证信息
     *
     * @param openid 微信
     * @return {@link UserAuthInfo}
     */
    @Override
    public UserAuthInfo getUserAuthInfoByOpenId(String openid) {
        UserAuthInfo userAuthInfo = this.mapper.selectOneWithRelationsByQueryAs(
                QueryWrapper.create().from(SysUserEntity.class)
                        .select(QueryMethods.column(SysUserEntity::getId).as(UserAuthInfo::getUserId))
                        .select(QueryMethods.column(SysUserEntity::getUsername).as(UserAuthInfo::getUsername))
                        .select(QueryMethods.column(SysUserEntity::getNickname).as(UserAuthInfo::getNickname))
                        .select(QueryMethods.column(SysUserEntity::getPassword).as(UserAuthInfo::getPassword))
                        .select(QueryMethods.column(SysUserEntity::getCreatedAt).as(UserAuthInfo::getCreatedAt))
                        .select(QueryMethods.column(SysUserEntity::getStatus).as(UserAuthInfo::getStatus)),
                UserAuthInfo.class
        );
        return userAuthInfo;
    }


    /**
     * 获取导出用户列表
     *
     * @param queryParams 查询参数
     * @return {@link List<UserExportDTO>} 导出用户列表
     */
    @Override
    public List<UserExportDTO> listExportUsers(UserPageQuery queryParams) {

        QueryWrapper query = QueryWrapper.create().from(SysUserEntity.class)
                .select(
                        QueryMethods.column(SysUserEntity::getId),
                        QueryMethods.column(SysUserEntity::getUsername),
                        QueryMethods.column(SysUserEntity::getNickname),
                        QueryMethods.column(SysUserEntity::getCreatedAt)
                )
                .where(SysUserEntity::getUsername).ne("root")
                .and(SysUserEntity::getStatus).eq(queryParams.getStatus(), queryParams.getStatus() != null);

        query.groupBy(SysUserEntity::getId);


        return this.listAs(
                query,
                UserExportDTO.class
        );
    }

    /**
     * 获取登录用户信息
     *
     * @return {@link UserInfoVO}   用户信息
     */
    @Override
    public UserInfoVO getCurrentUserInfo() {

        SysUserDetails sysUserDetails = SecurityUtils.getUser().orElse(new SysUserDetails());

        // details->VO
        UserInfoVO userInfoVO = userConverter.toUserInfoVo(sysUserDetails);

        // 用户角色集合
        Set<String> roles = SecurityUtils.getRoles();
        userInfoVO.setRoles(roles);

        // 用户权限集合
        List<SysPermissionEntity> permList = roleService.getCurUserPermList();
        Set<String> permSigns = permList.stream().map(SysPermissionEntity::getSign).collect(Collectors.toSet());
        userInfoVO.setPermissions(permSigns);

        // 用户角色名
        List<RolePageVO> roleNames = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(roles)) {
            roleNames.addAll(roleService.listAs(QueryWrapper.create()
                    .where(SysRoleEntity::getCompanyId).eq(sysUserDetails.getCompanyId())
                    .where(SysRoleEntity::getCode).in(roles), RolePageVO.class));
        }
        if (SecurityUtils.isMain()) {
            roleNames.add(RolePageVO.builder().name("主账号").build());
        }
        userInfoVO.setRoleVos(roleNames);

        // 门店名
        List<MerchantEntity> merchantNames = new ArrayList<>();
        userInfoVO.setMerchants(merchantNames);
        if (SecurityUtils.isMain()) {
            MerchantEntity entity = new MerchantEntity().setName("主账号");
            entity.setId(0L);
            merchantNames.add(entity);
            return userInfoVO;
        }
        Set<Long> merchantIds = sysUserDetails.getMerchantIds();
        if (CollectionUtil.isNotEmpty(merchantIds)) {
            merchantNames.addAll(merchantMapper.selectListByQuery(QueryWrapper.create()
                    .where(MERCHANT.ID.in(merchantIds))
                    .where(MERCHANT.COMPANY_ID.eq(SecurityUtils.getCompanyId()))));
        }
        return userInfoVO;
    }

    /**
     * 获取个人中心用户信息
     *
     * @param userId 用户ID
     * @return
     */
    @Override
    public UserProfileVO getUserProfile(Long userId) {
        return this.getOneAs(
                QueryWrapper.create().from(SysUserEntity.class)
                        .select(
                                QueryMethods.column(SysUserEntity::getId).as(UserBO::getId),
                                QueryMethods.column(SysUserEntity::getUsername).as(UserBO::getUsername),
                                QueryMethods.column(SysUserEntity::getNickname).as(UserBO::getNickname),
                                QueryMethods.column(SysUserEntity::getAvatar).as(UserBO::getAvatar),
                                QueryMethods.column(SysUserEntity::getStatus).as(UserBO::getStatus),
                                QueryMethods.column(SysUserEntity::getCreatedAt).as(UserBO::getCreatedAt),
                                QueryMethods.groupConcat(QueryMethods.column(SysRoleEntity::getName)).as(UserBO::getRoleNames)
                        )
                        .leftJoin(SysUserRoleEntity.class).on(q -> q.and(SysUserRoleEntity::getUserId).eq(SysUserEntity::getId))
                        .leftJoin(SysRoleEntity.class).on(q -> q.and(SysRoleEntity::getId).eq(SysUserRoleEntity::getRoleId))
                        .where(SysUserEntity::getId).eq(userId),
                UserProfileVO.class
        );
    }

    /**
     * 修改个人中心用户信息
     *
     * @param formData 表单数据
     * @return
     */
    @Override
    public boolean updateUserProfile(UserProfileForm formData) {
        Long userId = SecurityUtils.getUserId();
        SysUserEntity entity = userConverter.toEntity(formData);
        entity.setId(userId);
        return this.updateById(entity);
    }


    /**
     * 修改用户密码
     *
     * @param userId 用户ID
     * @param data   密码修改表单数据
     * @return
     */
    @Override
    @Transactional
    public boolean changePassword(Long userId, PasswordChangeForm data) {

        SysUserEntity user = this.getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        String oldPassword = data.getOldPassword().trim();
        // 校验确认密码
        if (!data.getConfirmPassword().trim().equals(data.getNewPassword().trim())) {
            throw new BusinessException("两次密码输入不一致");
        }
        // 校验新密码
        Matcher matcher = Pattern.compile(SecurityConstants.PASSWORD_PATTERN).matcher(data.getNewPassword().trim());
        if (!matcher.find()) {
             throw new BusinessException(SecurityConstants.PASSWORD_TIPS);
        }

        // 校验原密码
        if (!passwordEncoder.matches(oldPassword, user.getPassword())) {
            throw new BusinessException("原密码错误");
        }
        // 新旧密码不能相同
        if (passwordEncoder.matches(data.getNewPassword().trim(), user.getPassword().trim())) {
            throw new BusinessException("新密码不能与原密码相同");
        }

        String newPassword = data.getNewPassword().trim();
        boolean result = this.update(
                new SysUserEntity().setPassword(passwordEncoder.encode(newPassword)),
                QueryWrapper.create().eq(SysUserEntity::getId, userId)
        );

        if (result) {
            // 加入黑名单，重新登录
            SysUserDetails userDetails = SecurityUtils.getUser().orElse(new SysUserDetails());
            tokenService.blacklistToken(userDetails.getUserId());
        }

        // 记录操作日志
        OpLogUtils.appendOpLog("用户管理-修改密码", "用户修改密码: " + user.getUsername(),
            Map.of("用户ID", userId));

        return result;
    }

    /**
     * 重置密码
     */
    @Override
    @Transactional
    public boolean resetPassword(PasswordChangeForm data) {
        // 校验确认密码
        if (!data.getConfirmPassword().trim().equals(data.getNewPassword().trim())) {
            throw new BusinessException("两次密码输入不一致");
        }
        // 校验新密码
        Matcher matcher = Pattern.compile(SecurityConstants.PASSWORD_PATTERN).matcher(data.getNewPassword().trim());
        if (!matcher.find()) {
            throw new BusinessException(SecurityConstants.PASSWORD_TIPS);
        }
        String newPassword = data.getNewPassword().trim();
        boolean result = this.update(
                new SysUserEntity().setPassword(passwordEncoder.encode(newPassword)),
                QueryWrapper.create().eq(SysUserEntity::getId, data.getUserId())
        );
        if (result) {
            tokenService.blacklistToken(data.getUserId());
        }

        // 记录操作日志
        OpLogUtils.appendOpLog("用户管理-重置密码", "重置用户密码: " + data.getUserId(),
            Map.of("用户ID", data.getUserId()));

        return result;
    }

    /**
     * 发送验证码（需要在上一步校验手机号使用情况）
     * @param contact 手机号
     * @param type 场景
     * @return
     */
    @Override
    public boolean sendVerificationCode(String contact, Integer type) {
        // 随机生成6位验证码
        String code = RandomUtil.randomNumbers(6);
        String awaitKey = RedisConstants.MOBILE_VERIFICATION_CODE_AWAIT_PREFIX + contact;
        String codeKey = RedisConstants.MOBILE_VERIFICATION_CODE_PREFIX + contact;
        // 如果存在该key，则不能发送验证码
        Assert.isFalse(redisTemplate.hasKey(awaitKey), ResultCode.VERIFY_CODE_AWAIT_ERROR.getMsg());
        // 非生产环境
        if (!ContextUtils.isProd()) {
            LocalDate currentDate = LocalDate.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MMdd");
            code = currentDate.format(formatter);
            redisTemplate.opsForValue().set(codeKey, code, 2, TimeUnit.MINUTES);
            // 存入 redis 用于校验, 防止重复提交
            redisTemplate.opsForValue().set(awaitKey, code, 1, TimeUnit.MINUTES);
            return true;
        }

        long count = verifyCodeMapper.selectCountByQuery(QueryWrapper.create()
                .where(VERIFY_CODE.MOBILE.eq(contact))
                .where(VERIFY_CODE.CREATED_AT.ge(DateUtil.beginOfDay(new Date())))
                .where(VERIFY_CODE.CREATED_AT.le(DateUtil.endOfDay(new Date()))));
        Assert.isTrue(count < 20, ResultCode.VERIFY_CODE_OUT_LIMIT_ERROR.getMsg());
        // 存入 redis 用于校验, 防止重复提交
        redisTemplate.opsForValue().set(awaitKey, code, 1, TimeUnit.MINUTES);
        // 获取修改密码的模板code
        smsService.sendSms(contact, code);
        // 存入 redis 用于校验, 2分钟有效
        redisTemplate.opsForValue().set(codeKey, code, 2, TimeUnit.MINUTES);
        // ip地址
        String ipAddr = "";
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest  request = attributes.getRequest();
            ipAddr = IPUtils.getIpAddr(request);
        }
        verifyCodeMapper.insert(new VerifyCodeEntity().setCode(code).setType(type).setMobile(contact).setIp_address(ipAddr));
        return true;
    }

    /**
     * 修改当前用户手机号码
     * @return
     */
    @Override
    public boolean bindMobile(String username, String code, String sideCode) {
        Long currentUserId = SecurityUtils.getUserId();
        SysUserEntity currentUser = this.getById(currentUserId);
        String oldUserName = currentUser.getUsername();
        Assert.notNull(currentUser, ResultCode.USER_NOT_EXIST.getMsg());
        // 校验验证码
        verifyCode(username, code, 0, sideCode);
        // 更新手机号码
        String redisKey = SecurityConstants.USERNAME_VERIFY_KEY + username;
        RLock lock = redissonClient.getLock(redisKey);
        try {
            lock.lock(5, TimeUnit.SECONDS);
            // 再次检查是否被使用
            boolean usable = usernameUsable(username, 0L, currentUser.getCompanyId());
            Assert.isTrue(usable, ResultCode.USERNAME_USED.getMsg());
            currentUser.setUsername(username);
            this.mapper.update(currentUser);
        }finally {
            lock.unlock();
        }
        OpLogUtils.appendOpLog("用户管理-修改手机号", "修改手机号", Map.of("修改前", oldUserName, "修改后", username));
        // 删除登录信息，重新登录
        tokenService.blacklistToken(currentUser.getId());
        return true;
    }


    /**
     * 获取用户选项列表
     *
     * @return {@link List<Option<String>>} 用户选项列表
     */
    @Override
    public List<Option<String>> listUserOptions() {
        Long companyId = SecurityUtils.getCompanyId();
        boolean root = SecurityUtils.isRoot();
        List<SysUserEntity> list = this.list(QueryWrapper.create()
                .where(SysUserEntity::getCompanyId).eq(companyId, !root));
        if (CollectionUtil.isNotEmpty(list)) {
            return list.stream().map(user -> new Option<>(user.getId().toString(), user.getNickname())).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public boolean verifyPassword(String password) {
        SysUserDetails userDetails = SecurityUtils.getUser().orElse(new SysUserDetails());
        SysUserEntity userEntity = this.mapper.selectOneById(userDetails.getUserId());
        Assert.notNull(userEntity, ResultCode.USER_NOT_EXIST.getMsg());
        boolean result = passwordEncoder.matches(password, userEntity.getPassword());
        Assert.isTrue(result, ResultCode.USERNAME_OR_PASSWORD_ERROR.getMsg());
        return true;
    }

    @Override
    public boolean verifyCode(String username, String code, Integer isLogin, String sideCode) {
        // 校验验证码是否正确
        String verifyKey = RedisConstants.MOBILE_VERIFICATION_CODE_PREFIX + username;
        String blockKey = RedisConstants.MOBILE_VERIFICATION_BLOCK_PREFIX + username;
        // 判断是否被禁止登录
        if (redisTemplate.hasKey(blockKey) && isLogin.equals(1)) {
            throw new BusinessException(ResultCode.USERNAME_CODE_BLOCK_ERROR);
        }
        // 验证码失效
        String cacheCode = redisTemplate.opsForValue().get(verifyKey);
        if (StringUtils.isBlank(cacheCode)) {
            throw new BusinessException(ResultCode.USERNAME_CODE_NOTFOUND);
        }
        // 验证码错误
        if (!code.equals(cacheCode)) {
            if (isLogin.equals(1)) {
                String redisKey = RedisConstants.CODE_VERIFICATION_BLOCK_COUNT_PREFIX;
                if (sideCode.equals(SideEnum.PDA.getValue())) {
                    redisKey = RedisConstants.PDA_CODE_VERIFICATION_BLOCK_COUNT_PREFIX;
                }
                loginPassErrorHandle(username, redisKey);
            }
            throw new BusinessException(ResultCode.USERNAME_CODE_ERROR);
        }
        redisTemplate.delete(verifyKey);
        return true;
    }

    /**
     * 登录密码错误处理
     * @param username 用户名
     * @param prefix 计数缓存前缀
     */
    public void loginPassErrorHandle(String username, String prefix) {
        String blockCountKey = prefix + username;
        String blockKey = RedisConstants.MOBILE_VERIFICATION_BLOCK_PREFIX + username;
        // 如果错误次数大于等于5次，则锁定用户5分钟内不能登录
        String cache = redisTemplate.opsForValue().get(blockCountKey);
        int count = 0;
        if (StringUtils.isNotBlank(cache)) {
            count = Integer.parseInt(cache);
        }
        if (count++ >= 4) {
            redisTemplate.opsForValue().set(blockKey, String.valueOf(count), 2, TimeUnit.MINUTES);
            redisTemplate.delete(blockCountKey);
        }else {
            redisTemplate.opsForValue().set(blockCountKey, String.valueOf(count), 2, TimeUnit.MINUTES);
        }
    }

    private void exportUsers(QueryWrapper query, UserPageQuery queryParams) {
        if (CollectionUtil.isNotEmpty(queryParams.getIds())) {
            query.in(SysUserEntity::getId, queryParams.getIds());
        }
        ExcelUtil.of(this.mapper, query, UserPageVO.class, "user_list",  "用户列表")
                .getData((mapper, wrapper) -> {
                    if (mapper instanceof UserMapper userMapper) {
                        List<UserPageVO> voList = userMapper.selectListByQueryAs(wrapper, UserPageVO.class);
                        if (!voList.isEmpty()) {
                            Set<Long> ids = voList.stream().map(UserPageVO::getId).collect(Collectors.toSet());
                            ListFillUtil.of(voList)
                                    .build(listFillService::getUserRoleVosByUserId, ids,"id", "roles")
                                    .build(listFillService::getUserMerchantVosByUserId, ids,"id", "merchants")
                                    .handle();
                        }
                        return voList;
                    }
                    return new ArrayList<>();
                })
                .doExport();
    }
}
