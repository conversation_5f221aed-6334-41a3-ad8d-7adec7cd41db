package com.xc.boot.system.model.query;

import cn.hutool.db.sql.Direction;
import com.xc.boot.common.base.BasePageQuery;
import com.xc.boot.common.annotation.ValidField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 用户分页查询对象
 *
 * <AUTHOR>
 * @since 2022/1/14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "用户分页查询对象")
public class UserPageQuery extends BasePageQuery {

    @Schema(description = "手机号")
    private String username;

    @Schema(description = "姓名")
    private String nickname;

    @Schema(description = "门店ID")
    private Long merchantId;

    @Schema(description = "角色ID")
    private Long roleId;

    @Schema(description = "用户状态")
    private Integer status;

    @Schema(description = "时间区间")
    private Date[] timeRange;

    @Schema(description = "导出标识")
    private Integer export = 0;
}
