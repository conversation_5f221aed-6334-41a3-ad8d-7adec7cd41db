package com.xc.boot.system.model.query;

import com.xc.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * PDA包分页查询
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "PDA包分页查询")
public class PdaPackagePageQuery extends BasePageQuery {
    @Schema(description = "设备类型")
    private String type;

    @Schema(description = "版本号")
    private String version;
}