package com.xc.boot.system.model.enums;

import com.xc.boot.common.model.Option;
import com.xc.boot.shared.common.model.query.OptionQuery;
import lombok.Getter;

import java.util.List;
import java.util.Set;
import java.util.stream.Stream;

@Getter
public enum PdaEnum {
    SH_YC("Handheld", "上海玙初智能（handheld）"),
    SZ_YK_PDA("pda", "深圳优库（PDA）");

    private final String value;
    private final String label;

    PdaEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public static String getLabel(String value) {
        for (PdaEnum item : PdaEnum.values()) {
            if (item.value.equals(value)) {
                return item.label;
            }
        }
        return "";
    }

    /**
     * 将枚举值转换为 List<Option<Long>>
     * @return List<Option<Long>>
     */
    public static List<Option<String>> toOptions(OptionQuery query) {
        Stream<PdaEnum> values = Stream.of(values());
        if (query.getKeyword() != null) {
            values = values.filter(e -> e.getLabel().contains(query.getKeyword()));
        }
        if (query.getEcho() != null) {
            values = values.filter(e -> Set.of(query.getEcho().split(",")).contains(e.getValue().toString()));
        }
        return values.map(pda -> new Option<>(pda.getValue(), pda.getLabel())).toList();
    }
}
