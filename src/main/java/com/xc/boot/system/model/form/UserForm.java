package com.xc.boot.system.model.form;

import com.mybatisflex.annotation.RelationManyToMany;
import com.mybatisflex.annotation.RelationOneToMany;
import com.xc.boot.common.annotation.validGroup.Create;
import com.xc.boot.common.annotation.validGroup.Update;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import org.hibernate.validator.constraints.Range;

import java.util.List;

/**
 * 用户个人信息表单对象
 * <AUTHOR>
 * @since 2022/4/12 11:04
 */
@Schema(description = "用户个人信息表单对象")
@Data
public class UserForm {
    @Schema(description="用户ID")
    @NotNull(message = "用户ID不能为空",  groups = Update.class)
    private Long id;

    @Schema(description="昵称")
    @NotBlank(message = "昵称不能为空", groups = {Update.class})
    private String nickname;

    @Schema(description="性别")
    @NotNull(message = "性别不能为空",  groups = {Update.class})
    private Integer gender;

    @Schema(description="用户头像")
    private String avatar;

    @Schema(description="用户头像id")
    private Long avatarId;
}
