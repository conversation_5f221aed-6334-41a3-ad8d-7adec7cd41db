package com.xc.boot.system.model.query;

import com.xc.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 商家分页查询参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "商家分页查询参数")
public class CompanyPageQuery extends BasePageQuery {
    
    @Schema(description = "商家名称")
    private String name;
    
    @Schema(description = "商家账号")
    private String phone;
    
    @Schema(description = "联系人")
    private String contact;
    
    @Schema(description = "商家地址")
    private String address;
    
    @Schema(description = "商家状态(0:禁用|1:启用)")
    private Integer status;
    
    @Schema(description = "是否允许多门店")
    private Boolean isMultiple;
    
    @Schema(description = "创建时间范围[开始时间,结束时间]")
    private String[] createTimeRange;
} 